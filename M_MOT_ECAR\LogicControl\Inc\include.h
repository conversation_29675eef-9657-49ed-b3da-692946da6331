/*
 * include.h
 *
 *  Created on: Nov 26, 2020
 *      Author: RS
 */

#ifndef INC_INCLUDE_H_
#define INC_INCLUDE_H_


#include "Typedefine.h"
#include "Vardeclare.h"
#include "MsgProcess.h"
#include "DataProcess.h"
#include "GetInput.h"
#include "OtherFunction.h"
#include "faultProcess.h"
#include "FaultLight.h"
#include "Store.h"
#include "Can.h"
#include "PI.h"
#include "speedmode.h"
#include "HPM.h"
#include "torquemode.h"
#include "DriverOutput.h"
#include "AuxMCUComm.h"
#include <stdlib.h>

#include "MotorControl\Inc\MotorControl.h"

#endif /* INC_INCLUDE_H_ */
