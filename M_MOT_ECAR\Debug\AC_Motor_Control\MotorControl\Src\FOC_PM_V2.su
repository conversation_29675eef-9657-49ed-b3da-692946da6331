../AC_Motor_Control/MotorControl/Src/FOC_PM_V2.cpp:50:6:void CFOC_PM_V2::MtrPrmsInit()	24	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V2.cpp:39:6:void CFOC_PM_V2::LoadMtrPrms(const FOCPMV1MtrPrms_t*)	8	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V2.cpp:361:6:void setFOCPMV2Prms_t(const FOCPMV2Prms_t*)	4	static
../STM32G4_Drivers/Inc\Timer.h:58:6:void CTimer<CntType>::SetStart() [with CntType = short unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:71:9:CntType CTimer<CntType>::GetTimeElapsed(uint8_t) [with CntType = short unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:106:6:void CTimer<CntType>::SaveSmpToRec() [with CntType = short unsigned int]	0	static
../AC_Motor_Control/MotorControl/Src/../Inc/../../Common/Inc/UserMaths.h:145:7:float CDifferential<InType>::GetVal() [with InType = float]	24	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V2.cpp:128:6:void CFOC_PM_V2::NorPrcs()	32	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V2.cpp:274:6:void CFOC_PM_V2::ShtDwnPrcs()	16	static
