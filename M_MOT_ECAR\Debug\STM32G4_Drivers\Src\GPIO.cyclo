../STM32G4_Drivers/Src/GPIO.cpp:16:1:CGPIO::CGPIO()	1
../STM32G4_Drivers/Src/GPIO.cpp:67:6:void CGPIO::SelectPin(GPIOPin_t)	8
../STM32G4_Drivers/Src/GPIO.cpp:109:6:void CGPIO::SetMode(GPIOMode_t)	8
../STM32G4_Drivers/Src/GPIO.cpp:20:1:CGPIO::CGPIO(GPIOPin_t, GPIOMode_t)	1
../STM32G4_Drivers/Src/GPIO.cpp:37:6:void CGPIO::Init(GPIOPin_t, GPIOMode_t)	1
../STM32G4_Drivers/Src/GPIO.cpp:151:6:void CGPIO::SetLevel(uint8_t)	1
../STM32G4_Drivers/Src/GPIO.cpp:28:1:CGPIO::CGPIO(GPIOPin_t, GPIOMode_t, uint8_t)	1
../STM32G4_Drivers/Src/GPIO.cpp:45:6:void CGPIO::Init(GPIOPin_t, GPIOMode_t, uint8_t)	1
../STM32G4_Drivers/Src/GPIO.cpp:54:6:void CGPIO::Init(const GPIOPrms_t*)	2
../STM32G4_Drivers/Src/GPIO.cpp:160:6:void CGPIO::Toggle()	1
../STM32G4_Drivers/Src/GPIO.cpp:168:9:uint8_t CGPIO::GetLevel()	1
../STM32G4_Drivers/Src/GPIO.cpp:177:6:void CGPIO::SetActive(uint8_t)	1
../STM32G4_Drivers/Src/GPIO.cpp:186:9:uint8_t CGPIO::GetActive()	1
../STM32G4_Drivers/Src/GPIO.cpp:203:6:void CGPIO::SetEXTI(uint8_t, void (*)())	18
../STM32G4_Drivers/Src/GPIO.cpp:298:6:void CGPIO::EnableEXTI()	1
../STM32G4_Drivers/Src/GPIO.cpp:306:6:void CGPIO::DisableEXTI()	1
../STM32G4_Drivers/Src/GPIO.cpp:322:6:void EXTI0_IRQHandler()	1
../STM32G4_Drivers/Src/GPIO.cpp:331:6:void EXTI1_IRQHandler()	1
../STM32G4_Drivers/Src/GPIO.cpp:340:6:void EXTI2_IRQHandler()	1
../STM32G4_Drivers/Src/GPIO.cpp:349:6:void EXTI3_IRQHandler()	1
../STM32G4_Drivers/Src/GPIO.cpp:358:6:void EXTI4_IRQHandler()	1
../STM32G4_Drivers/Src/GPIO.cpp:367:6:void EXTI9_5_IRQHandler()	3
../STM32G4_Drivers/Src/GPIO.cpp:383:6:void EXTI15_10_IRQHandler()	3
