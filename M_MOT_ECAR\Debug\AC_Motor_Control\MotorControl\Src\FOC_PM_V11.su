../AC_Motor_Control/MotorControl/Src/FOC_PM_V11.cpp:33:1:CFOC_PM_V11::CFOC_PM_V11()	16	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V11.cpp:68:6:void CFOC_PM_V11::MtrPrmsInit()	24	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V11.cpp:57:6:void CFOC_PM_V11::LoadMtrPrms(const FOCPMV11MtrPrms_t*)	8	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V11.cpp:169:6:void CFOC_PM_V11::KFldWknMaxCalc()	32	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V11.cpp:231:6:void CFOC_PM_V11::Park()	16	static
../STM32G4_Drivers/Inc\Timer.h:58:6:void CTimer<CntType>::SetStart() [with CntType = long unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:89:9:uint8_t CTimer<CntType>::CheckTimeElps(CntType) [with CntType = long unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:71:9:CntType CTimer<CntType>::GetTimeElapsed(uint8_t) [with CntType = long unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:58:6:void CTimer<CntType>::SetStart() [with CntType = short unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:71:9:CntType CTimer<CntType>::GetTimeElapsed(uint8_t) [with CntType = short unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:106:6:void CTimer<CntType>::SaveSmpToRec() [with CntType = short unsigned int]	0	static
../AC_Motor_Control/MotorControl/Src/..\Inc\../../Common/Inc/UserMaths.h:145:7:float CDifferential<InType>::GetVal() [with InType = float]	24	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V11.cpp:253:6:void CFOC_PM_V11::NorPrcs()	16	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V11.cpp:462:6:void CFOC_PM_V11::IdIqDrctCtl(float, float)	16	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V11.cpp:533:6:void CFOC_PM_V11::PssvEcdrOfst()	24	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V11.cpp:683:6:void CFOC_PM_V11::ActvEcdrOfst()	32	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V11.cpp:814:6:void CFOC_PM_V11::ShtDwnPrcs()	16	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V11.cpp:901:6:void CFOC_PM_V11::ClbPrcs()	16	static
