Faults.c:18:6:<PERSON>g<PERSON>ost<PERSON>rr<PERSON>hk	24	static
Faults.c:100:6:OverSpeedErrChk	24	static
Faults.c:248:6:Dc<PERSON>verVoltErrChk	16	static
Faults.c:301:6:Dc<PERSON>nderVoltErrChk	16	static
Faults.c:365:6:AcOverCur2ErrChk	0	static
Faults.c:373:6:AcOverCur1ErrChk	0	static
Faults.c:400:6:InvOverTempErrChk	24	static
Faults.c:523:6:PhaseBrkErrChk	0	static
Faults.c:531:6:DisChgErrChk	0	static
Faults.c:549:6:MotOverTempErrChk	24	static
Faults.c:683:6:VoltageAcquisitionErrChk	24	static
Faults.c:717:6:PosSensorErrChk	0	static
Faults.c:726:6:CurSensorErrChk	0	static
Faults.c:735:6:<PERSON><PERSON><PERSON>tallErrChk	32	static
Faults.c:847:6:ParErrChk	0	static
Faults.c:856:6:ThrottleErrChk	0	static
Faults.c:885:6:OperationSequenceErr	0	static
Faults.c:923:6:LogicPowerErrChk	32	static
Faults.c:1195:6:HistroyFaultRefresh	24	static
Faults.c:1225:6:FaultGrade	20	static
Faults.c:1318:6:KHD_FalutRefresh	8	static
Faults.c:1501:6:FaultChkSlow	32	static
Faults.c:1554:7:MaxValue	0	static
Faults.c:1567:7:max	0	static
Faults.c:1583:7:MinValue	0	static
Faults.c:1596:7:min	0	static
Faults.c:1612:6:FaultLmtCtrl	32	static
