/*
 * MsgProcess.c
 *
 *  Created on: Nov 24, 2020
 *      Author: RS
 */
#include "include.h"
#include "main.h"
#include "Inc\ADC.h"
#include "uds.h"

extern FDCAN_TxHeaderTypeDef TxHeader;
extern FDCAN_RxHeaderTypeDef RxHeader;
extern FDCAN_HandleTypeDef hfdcan1;

static uint8_t RxData[8];

void MsgConfig(void)
{
//注册接收帧
	//下程序ID
	PC_DownPrgm.ID = 0;
	PC_DownPrgm.Dir = CANDir_RX;
	PC_DownPrgm.IDType = CANID_Extended;
	CanAddNewFrame(&PC_DownPrgm);
#ifdef SANTROLL_UDS_COMBINE

	UDS_RX_PHYSICAL_CAN.ID = UDS_RX_PHYSICAL_CAN_ID;
	UDS_RX_PHYSICAL_CAN.Dir = CANDir_RX;
	UDS_RX_PHYSICAL_CAN.IDType = CANID_Standard;
	CanAddNewFrame(&UDS_RX_PHYSICAL_CAN);

	UDS_RX_FUNCTIONAL_CAN.ID = UDS_RX_FUNCTIONAL_CAN_ID;
	UDS_RX_FUNCTIONAL_CAN.Dir = CANDir_RX;
	UDS_RX_FUNCTIONAL_CAN.IDType = CANID_Standard;
	CanAddNewFrame(&UDS_RX_FUNCTIONAL_CAN);

	UDS_TX_PHYSICAL_CAN.ID = UDS_TX_PHYSICAL_CAN_ID;
	UDS_TX_PHYSICAL_CAN.Dir = CANDir_TX;
	UDS_TX_PHYSICAL_CAN.IDType = CANID_Standard;
	UDS_TX_PHYSICAL_CAN.FrameType = CANFrame_Data;
	UDS_TX_PHYSICAL_CAN.DataLength = CANDlc_Byte_8;
	CanAddNewFrame(&UDS_TX_PHYSICAL_CAN);
#endif

	//客户端ID
	Can_PC_RX.ID = PcRxId;
	Can_PC_RX.Dir = CANDir_RX;
	Can_PC_RX.IDType = CANID_Extended;
	CanAddNewFrame(&Can_PC_RX);
	//手编蓝牙协议ID
	Can_HPM_RX0.ID = HPM_RX_ID0;
	Can_HPM_RX0.Dir = CANDir_RX;
	Can_HPM_RX0.IDType = CANID_Extended;
	CanAddNewFrame(&Can_HPM_RX0);

	Can_HPM_RX1.ID = HPM_RX_ID1;
	Can_HPM_RX1.Dir = CANDir_RX;
	Can_HPM_RX1.IDType = CANID_Extended;
	CanAddNewFrame(&Can_HPM_RX1);

	Can_HPM_RX2.ID = HPM_RX_ID2;
	Can_HPM_RX2.Dir = CANDir_RX;
	Can_HPM_RX2.IDType = CANID_Extended;
	CanAddNewFrame(&Can_HPM_RX2);
	//电压校准ID
	VOLTADJ_RX1.ID = VOLTADJ_RX_ID1;
	VOLTADJ_RX1.Dir = CANDir_RX;
	VOLTADJ_RX1.IDType = CANID_Extended;
	CanAddNewFrame(&VOLTADJ_RX1);

	VOLTADJ_RX2.ID = VOLTADJ_RX_ID2;
	VOLTADJ_RX2.Dir = CANDir_RX;
	VOLTADJ_RX2.IDType = CANID_Extended;
	CanAddNewFrame(&VOLTADJ_RX2);

#if MODE == ECAR
	CCU_MCU_Ctrl.ID = CCU_MCU_CTRL_ID;
	CCU_MCU_Ctrl.Dir = CANDir_RX;
	CCU_MCU_Ctrl.IDType = CANID_Standard;
	CanAddNewFrame(&CCU_MCU_Ctrl);

	CCU_MCU_Limit.ID = CCU_MCU_LIMIT_ID;
	CCU_MCU_Limit.Dir = CANDir_RX;
	CCU_MCU_Limit.IDType = CANID_Standard;
	CanAddNewFrame(&CCU_MCU_Limit);

#elif MODE == DRAGTEST
	VCU1_RX.ID = (g_sComFlag.bDragMode == DRAG_20)? VCU1_RX_ID : VCU2_RX_ID;
	VCU1_RX.Dir = CANDir_RX;
	VCU1_RX.IDType = CANID_Extended;
	CanAddNewFrame(&VCU1_RX);

	VCU3_RX.ID = VCU3_RX_ID;
	VCU3_RX.Dir = CANDir_RX;
	VCU3_RX.IDType = CANID_Extended;
	CanAddNewFrame(&VCU3_RX);
#endif

//注册发送ID
	Can_PC_TX1.ID = PcTx1Id;
	Can_PC_TX1.Dir = CANDir_TX;
	Can_PC_TX1.IDType = CANID_Extended;
	Can_PC_TX1.FrameType = CANFrame_Data;
	Can_PC_TX1.DataLength = CANDlc_Byte_8;
	CanAddNewFrame(&Can_PC_TX1);

	Can_PC_TX2.ID = PcTx2Id;
	Can_PC_TX2.Dir = CANDir_TX;
	Can_PC_TX2.IDType = CANID_Extended;
	Can_PC_TX2.FrameType = CANFrame_Data;
	Can_PC_TX2.DataLength = CANDlc_Byte_8;
	CanAddNewFrame(&Can_PC_TX2);

	Can_HPM_TX0.ID = HPM_TX_ID0;
	Can_HPM_TX0.Dir = CANDir_TX;
	Can_HPM_TX0.IDType = CANID_Extended;
	Can_HPM_TX0.FrameType = CANFrame_Data;
	Can_HPM_TX0.DataLength = CANDlc_Byte_8;
	CanAddNewFrame(&Can_HPM_TX0);

	Can_HPM_TX1.ID = HPM_TX_ID1;
	Can_HPM_TX1.Dir = CANDir_TX;
	Can_HPM_TX1.IDType = CANID_Extended;
	Can_HPM_TX1.FrameType = CANFrame_Data;
	Can_HPM_TX1.DataLength = CANDlc_Byte_8;
	CanAddNewFrame(&Can_HPM_TX1);

	Can_HPM_TX2.ID = HPM_TX_ID2;
	Can_HPM_TX2.Dir = CANDir_TX;
	Can_HPM_TX2.IDType = CANID_Extended;
	Can_HPM_TX2.FrameType = CANFrame_Data;
	Can_HPM_TX2.DataLength = CANDlc_Byte_8;

	CanAddNewFrame(&Can_HPM_TX2);
	VOLTADJ_TX.ID = VOLTADJ_TX_ID;
	VOLTADJ_TX.Dir = CANDir_TX;
	VOLTADJ_TX.IDType = CANID_Extended;
	VOLTADJ_TX.FrameType = CANFrame_Data;
	VOLTADJ_TX.DataLength = CANDlc_Byte_8;
	CanAddNewFrame(&VOLTADJ_TX);

	Can_TEST_TX.ID = TestTx1Id;
	Can_TEST_TX.Dir = CANDir_TX;
	Can_TEST_TX.IDType = CANID_Extended;
	Can_TEST_TX.FrameType = CANFrame_Data;
	Can_TEST_TX.DataLength = CANDlc_Byte_8;
	CanAddNewFrame(&Can_TEST_TX);

	Can_TEST_TX2.ID = TestTx2Id;
	Can_TEST_TX2.Dir = CANDir_TX;
	Can_TEST_TX2.IDType = CANID_Extended;
	Can_TEST_TX2.FrameType = CANFrame_Data;
	Can_TEST_TX2.DataLength = CANDlc_Byte_8;
	CanAddNewFrame(&Can_TEST_TX2);

#if MODE == ECAR
	MCU_State1.ID = MCU_State1_ID;
	MCU_State1.Dir = CANDir_TX;
	MCU_State1.IDType = CANID_Standard;
	MCU_State1.FrameType = CANFrame_Data;
	MCU_State1.DataLength = CANDlc_Byte_8;
	CanAddNewFrame(&MCU_State1);

	MCU_State2.ID = MCU_State2_ID;
	MCU_State2.Dir = CANDir_TX;
	MCU_State2.IDType = CANID_Standard;
	MCU_State2.FrameType = CANFrame_Data;
	MCU_State2.DataLength = CANDlc_Byte_8;
	CanAddNewFrame(&MCU_State2);

	MCU_ErrInfo.ID = MCU_ErrInfo_ID;
	MCU_ErrInfo.Dir = CANDir_TX;
	MCU_ErrInfo.IDType = CANID_Standard;
	MCU_ErrInfo.FrameType = CANFrame_Data;
	MCU_ErrInfo.DataLength = CANDlc_Byte_8;
	CanAddNewFrame(&MCU_ErrInfo);

	MCU_VersionInfo.ID = MCU_VersionInfo_ID;
	MCU_VersionInfo.Dir = CANDir_TX;
	MCU_VersionInfo.IDType = CANID_Standard;
	MCU_VersionInfo.FrameType = CANFrame_Data;
	MCU_VersionInfo.DataLength = CANDlc_Byte_8;
	CanAddNewFrame(&MCU_VersionInfo);

	Test1.ID = 0X100;
	Test1.Dir = CANDir_TX;
	Test1.IDType = CANID_Standard;
	Test1.FrameType = CANFrame_Data;
	Test1.DataLength = CANDlc_Byte_8;
	CanAddNewFrame(&Test1);

	Test2.ID = 0X101;
	Test2.Dir = CANDir_TX;
	Test2.IDType = CANID_Standard;
	Test2.FrameType = CANFrame_Data;
	Test2.DataLength = CANDlc_Byte_8;
	CanAddNewFrame(&Test2);

	Test3.ID = 0X102;
	Test3.Dir = CANDir_TX;
	Test3.IDType = CANID_Standard;
	Test3.FrameType = CANFrame_Data;
	Test3.DataLength = CANDlc_Byte_8;
	CanAddNewFrame(&Test3);

	Test4.ID = 0X103;
	Test4.Dir = CANDir_TX;
	Test4.IDType = CANID_Standard;
	Test4.FrameType = CANFrame_Data;
	Test4.DataLength = CANDlc_Byte_8;
	CanAddNewFrame(&Test4);

	Test5.ID = 0X104;
	Test5.Dir = CANDir_TX;
	Test5.IDType = CANID_Standard;
	Test5.FrameType = CANFrame_Data;
	Test5.DataLength = CANDlc_Byte_8;
	CanAddNewFrame(&Test5);
#elif MODE == DRAGTEST
	VCU1_Sta1.ID = (g_sComFlag.bDragMode == DRAG_20) ? VCU1_STA1_ID : VCU2_STA1_ID;
	VCU1_Sta1.Dir = CANDir_TX;
	VCU1_Sta1.IDType = CANID_Extended;
	VCU1_Sta1.FrameType = CANFrame_Data;
	VCU1_Sta1.DataLength = CANDlc_Byte_8;
	CanAddNewFrame(&VCU1_Sta1);

	VCU1_Sta2.ID = (g_sComFlag.bDragMode == DRAG_20) ? VCU1_STA2_ID : VCU2_STA2_ID;
	VCU1_Sta2.Dir = CANDir_TX;
	VCU1_Sta2.IDType = CANID_Extended;
	VCU1_Sta2.FrameType = CANFrame_Data;
	VCU1_Sta2.DataLength = CANDlc_Byte_8;
	CanAddNewFrame(&VCU1_Sta2);

	VCU1_Sta3.ID = (g_sComFlag.bDragMode == DRAG_20) ? VCU1_STA3_ID : VCU2_STA3_ID;
	VCU1_Sta3.Dir = CANDir_TX;
	VCU1_Sta3.IDType = CANID_Extended;
	VCU1_Sta3.FrameType = CANFrame_Data;
	VCU1_Sta3.DataLength = CANDlc_Byte_8;
	CanAddNewFrame(&VCU1_Sta3);

	VCU1_Sta4.ID = (g_sComFlag.bDragMode == DRAG_20) ? VCU1_STA4_ID : VCU2_STA4_ID;
	VCU1_Sta4.Dir = CANDir_TX;
	VCU1_Sta4.IDType = CANID_Extended;
	VCU1_Sta4.FrameType = CANFrame_Data;
	VCU1_Sta4.DataLength = CANDlc_Byte_8;
	CanAddNewFrame(&VCU1_Sta4);

	VCU3_TX.ID = VCU3_TX_ID;
	VCU3_TX.Dir = CANDir_TX;
	VCU3_TX.IDType = CANID_Extended;
	VCU3_TX.FrameType = CANFrame_Data;
	VCU3_TX.DataLength = CANDlc_Byte_8;
	CanAddNewFrame(&VCU3_TX);
#endif
}

void MsgProcess(void)
{
//#warning 自动复位下程序
	static uint16_t DownPrgmCnt = 0;
	if (PC_DownPrgm.Pending == 1)
	{
		if (PC_DownPrgm.MDL == 0x1FFFFFF)
		{
			if (PC_DownPrgm.MDH == 0x60000000 || PC_DownPrgm.MDH == 0x20000000)
			{
				if (DownPrgmCnt++ > 100)
				{
					__disable_irq();
					HAL_FLASH_Unlock();
					FLASH_EraseInitTypeDef EraseInitStruct;
					uint32_t PageError = 0;

					EraseInitStruct.TypeErase = FLASH_TYPEERASE_PAGES;
					EraseInitStruct.Page = 24;
					EraseInitStruct.NbPages = 1;
					EraseInitStruct.Banks = FLASH_BANK_1;

					HAL_StatusTypeDef erase_status = HAL_FLASHEx_Erase(
							&EraseInitStruct, &PageError);

					HAL_StatusTypeDef flash_status = HAL_FLASH_Program(
							FLASH_TYPEPROGRAM_DOUBLEWORD,
							SANTROLL_FLAG_ADDRESS, (uint64_t) VALID);

					HAL_FLASH_Lock();
					__enable_irq();
					NVIC_SystemReset();
				}
			}
		}
		PC_DownPrgm.Pending = 0;
	}

	if (UDS_RX_PHYSICAL_CAN.Pending == 1 || UDS_RX_FUNCTIONAL_CAN.Pending == 1)
	{
		uint8_t rxData[8];

		if (UDS_RX_PHYSICAL_CAN.Pending == 1)
		{
			if (CanReadFrame(&UDS_RX_PHYSICAL_CAN, rxData))
			{
				RxHeader.Identifier = UDS_RX_PHYSICAL_CAN_ID;
				RxHeader.IdType = FDCAN_STANDARD_ID;
				RxHeader.DataLength = FDCAN_DLC_BYTES_8;
				RxHeader.RxFrameType = FDCAN_DATA_FRAME;

				UDS_Result A = UDS_ProcessMessage(&hfdcan1, &RxHeader, rxData);
			}
		}

		if (UDS_RX_FUNCTIONAL_CAN.Pending == 1)
		{
			if (CanReadFrame(&UDS_RX_FUNCTIONAL_CAN, rxData))
			{
				RxHeader.Identifier = UDS_RX_FUNCTIONAL_CAN_ID;
				RxHeader.IdType = FDCAN_STANDARD_ID;
				RxHeader.DataLength = FDCAN_DLC_BYTES_8;
				RxHeader.RxFrameType = FDCAN_DATA_FRAME;

				UDS_Result A = UDS_ProcessMessage(&hfdcan1, &RxHeader, rxData);
			}
		}
	}

	Pc_MsgProcess();
	HPMMsgProcess();
	VoltAdjMsgProcess();

#if MODE == ECAR
	ECUMsgPrecess();
#elif MODE == DRAGTEST
	VCUMsgPrecess();
#endif
}

void Pc_MsgProcess(void)
{
	uint8_t Buff[8];
	static int16_t l_uccount = 0;
	if (Can_PC_RX.Pending == 1)
	{
		CanReadFrame(&Can_PC_RX, Buff);
		switch (Buff[0])
		{
		case 0xA0:
		{
			break;
		}
		case KEEPCONECT:
		{
			g_sComFlag.bNewPcCan = 1; //通信维持有效
			g_sRunVar.uiNewPC_CanCnt = 0;
			break;
		}
		case CLEARFAULT_CMD:
		{
			g_sComFlag.bClearHisFault = 1; //清除标志置1
			//操作EE
			for (l_uccount = 0; l_uccount < FAULT_FLAG_COUNT; l_uccount++) //历史故障数组清0
			{
				g_uiHistroyFault[l_uccount] = 0;
			}
			g_sComFlag.bClearHisFaultFbk = 1; //默认反馈清除成功
			for (l_uccount = 0; l_uccount < FAULT_FLAG_COUNT; l_uccount++)//分开清除历史故障，确保历史故障数组清除完成（存在EEPROM故障时,不在对EEPROM操作）
			{
				if (g_sComFlag.bClearHisFaultFbk != 0)
				{
					g_sComFlag.bClearHisFaultFbk = WriteParm(
							FAULT_ADD_IN_EEPROM + l_uccount, 0);
				}
				else
					g_sComFlag.bClearHisFaultFbk = 0;
			}
			break;
		}
		case READEEALL_CMD:
		{
			g_sRunVar.uiSendEE_All = 0;	//从头开始发送EE数据
			break;
		}
		case READEEINDEX_CMD:
		{
			g_sComFlag.bReadIndexEE = 1;
			g_sRunVar.uiSendEEIndex = Buff[2];
			break;
		}
		case WRITEEEINDEX_CMD:
		{
			uint16_t l_uiAdd; //写入参数的地址
			int16_t l_uiWriteData;
			uint16_t l_uiReadData;
			g_sComFlag.bWriteEE = 1;
			g_sRunVar.uiWriteEEIndex = Buff[2];
			g_sRunVar.uiWriteEEOffset = Buff[3];
			l_uiAdd = g_sRunVar.uiWriteEEIndex * 3 + g_sRunVar.uiWriteEEOffset;
			l_uiWriteData = ((uint16_t) Buff[5] << 8) | Buff[4];
			//判断范围地址最大255；数据范围不溢出
			if ((l_uiAdd <= CONFIG_PARM_NUM - 1)
					&& ((g_iParmRange[l_uiAdd][0] <= l_uiWriteData)
							&& (l_uiWriteData <= g_iParmRange[l_uiAdd][1])))
			{
				l_uiReadData = ReadParm(l_uiAdd);
				//不相等写入；相等直接反馈成功
				if (l_uiReadData != l_uiWriteData)
				{
					//判断是否需要写入EE（调试参数不写入EE）
					//这里要是不或上209，则参数iWriteEEPROMEn=1,上位机没法写入
					if ((g_uUserParm.iWriteEEPROMEn == 0) || (l_uiAdd == 209)) //允许写入EEPROM
					{
						g_sComFlag.bWriteEE_Result = WriteParm(l_uiAdd,
								l_uiWriteData);	        //将配置参数值写入EE地址
					}
					else
					{
						g_sComFlag.bWriteEE_Result = 1; //直接返回成功
					}
					//写入成功更新运行EE参数
					if (g_sComFlag.bWriteEE_Result)                    //EE写参数成功
					{
						g_sComFlag.bWriteParm = 1;           //置位写参数标志，用于特殊参数的处理
						g_sRunVar.ucParmNum = l_uiAdd;                  //更新参数序号
						g_uUserParm.iParm[l_uiAdd] = l_uiWriteData;	//mark 实时更新需要修改数组的参数值
						g_sComFlag.bWriteEE_Result = 1;
					}
					else
					{
						g_sComFlag.bWriteEE_Result = 0;
					}
				}
				else                                                 //写入与原始数据相等
				{
					g_sComFlag.bWriteEE_Result = 1; //成功
				}
			}
			else
			{
				g_sComFlag.bWriteEE_Result = 0; //失败
			}
			break;
		}
		case REALTIMEPLOT:
		{
			g_sComFlag.bRealTimePlot = 1;
			g_sRunVar.ucPoltCanLostCnt = 0;
			//判断帧号
			g_sRunVar.ucPoltFrameNum = Buff[1];
			switch (g_sRunVar.ucPoltFrameNum)
			{
			case 0:
				g_sRunVar.ucPoltIndexNum[0] = Buff[2];
				g_sRunVar.ucPoltIndexNum[1] = Buff[3];
				g_sRunVar.ucPoltIndexNum[2] = Buff[4];
				g_sRunVar.ucPoltIndexNum[3] = Buff[5];
				g_sRunVar.ucPoltIndexNum[4] = Buff[6];
				g_sRunVar.ucPoltIndexNum[5] = Buff[7];
				break;
			case 1:
				g_sRunVar.ucPoltIndexNum[6] = Buff[2];
				g_sRunVar.ucPoltIndexNum[7] = Buff[3];
				g_sRunVar.ucPoltTotalNum = Buff[6];
				break;
			default:
				break;
			}
			break;
		}
		default:
			break;
		}
	}
}
void Reverse_array(uint8_t *in, uint8_t *out)
{
	out[0] = in[7];
	out[1] = in[6];
	out[2] = in[5];
	out[3] = in[4];
	out[4] = in[3];
	out[5] = in[2];
	out[6] = in[1];
	out[7] = in[0];
}

uint8_t CheckSum(uint8_t *in)
{
	uint8_t Sum = 0;
	uint8_t i = 0;

	for (i = 0; i < 7; i++)
	{
		Sum += in[i];
	}

	return Sum ^ 0xFF;
}

void ECUMsgPrecess(void)
{
	uint8_t Buff[8];

	if (CCU_MCU_Ctrl.Pending)
	{
		g_sRunVar.iECUTimeCnt = 0;

		CanReadFrame(&CCU_MCU_Ctrl, Buff);

		if (Buff[7] != CheckSum(Buff))	//若不等于报故障，指令正常响应
		{
			g_uMonitorFault.bCCUCheckSum = 1;
			Mtr1CtrlItfc.StatReq = MtrStatReq_ShtDwn;        //关使能
		}
		else
		{
			g_uMonitorFault.bCCUCheckSum = 0;
		}

		Reverse_array(Buff, g_CCU_MCU_Ctrl.ucData);
	}

	if (CCU_MCU_Limit.Pending)
	{
		CanReadFrame(&CCU_MCU_Limit, Buff);

		if (Buff[7] != CheckSum(Buff))
		{
			g_uMonitorFault.bCCUCheckSum = 1;
			Mtr1CtrlItfc.StatReq = MtrStatReq_ShtDwn;        //关使能
		}
		else
		{
			g_uMonitorFault.bCCUCheckSum = 0;
		}

		Reverse_array(Buff, g_CCU_MCU_Limit.ucData);
	}
}

void VCUMsgPrecess(void)
{
	if (VCU1_RX.Pending)
	{
		CanReadFrame(&VCU1_RX, g_VCU1.ucData);
		if ((g_VCU1.bEN == 1) && (g_VCU1.bMode == DRAG_TRQ))        //扭矩模式才计加载次数
		{
			g_sRunVar.iECUTimeCnt = 0;
			g_sRunVar.LoadCmdTimes++;
		}
	}
	if ((VCU3_RX.Pending) && (g_VCU1.bMode == DRAG_TRQ))
	{
		if (VCU3_RX.MD0)
		{
			VCU3_RX.MD0 = 0;
			g_sRunVar.LoadCmdTimes = 0;        //加载指令次数清零
		}
		if (VCU3_RX.MD1)
		{
			VCU3_RX.MD1 = 0;
			g_sRunVar.LoadCycleTimes++;
		}
		VCU3_RX.Pending = 0;

		VCU3_TX.MD01 = g_sRunVar.LoadCmdTimes;
		VCU3_TX.MD23 = g_sRunVar.LoadCycleTimes;
		VCU3_TX.Pending = 1;
	}
	else if (VCU3_RX.Pending)
	{
		VCU3_RX.Pending = 0;
	}
}

void VoltAdjMsgProcess(void)
{
	if (VOLTADJ_RX2.Pending)
	{
		VOLTADJ_RX2.Pending = 0;
		if (g_sComFlag.bDragMode == DRAG_20)
		{
			g_sRunVar.cStartAdjustVolt = VOLTADJ_RX2.MD7;
		}
		else if (g_sComFlag.bDragMode == DRAG_60)
		{
			g_sRunVar.cStartAdjustVolt = VOLTADJ_RX2.MD6;
		}
		if (g_sRunVar.cStartAdjustVolt == 2)        //上位机发校准结束指令时再回复一帧
		{
			g_sRunVar.AdjustVoltSta = 0;
			VOLTADJ_TX.MD01 = g_uUserParm.iFactorBat;
			VOLTADJ_TX.MD23 = g_uUserParm.iFactorCap;
			VOLTADJ_TX.MD45 = 0;
			VOLTADJ_TX.MD67 = 0;
			VOLTADJ_TX.Pending = 1;
		}
		if (g_sRunVar.cStartAdjustVolt == 4)        //上位机发校准结束指令时再回复一帧
		{
			g_sRunVar.AdjustVoltSta = 0;
			VOLTADJ_TX.MD01 = g_uUserParm.iFactorSensorU;
			VOLTADJ_TX.MD23 = g_uUserParm.iFactorSensorV;
			VOLTADJ_TX.MD45 = 0;
			VOLTADJ_TX.MD67 = 0;
			VOLTADJ_TX.Pending = 1;
		}
	}
	if (VOLTADJ_RX1.Pending)
	{
		VOLTADJ_RX1.Pending = 0;
		g_sRunVar.iAdjustVolt = VOLTADJ_RX1.MD01;
		//电流传感器安装顺序UW,U相为电流1，W相为电流2
		g_sRunVar.iAdjustCrnt1 = VOLTADJ_RX1.MD23;        //工装采集的U相电流
		g_sRunVar.iAdjustCrnt2 = VOLTADJ_RX1.MD45;        //工装采集的W相电流
	}
}

void VoltAdjust(void)
{
	static uint16_t Cnt = 0;

	if (g_sRunVar.cStartAdjustVolt == 1)
	{
		uint16_t AdBatVolt = RglrSqDMABuf[ADCCh_KSI];        //电压AD值
		uint16_t AdCapVolt = RglrSqDMABuf[ADCCh_VBus];
		float BatVolt = 0;        //电池电压
		float CapVolt = 0;
		static float BatVoltFilt = 0;        //滤波后电池电压
		static float CapVoltFilt = 0;
		uint16_t BatFactor = 0;        //校准系数
		uint16_t CapFactor = 0;
		uint8_t EEWriteSta = 0;
		Cnt++;

		BatVolt = (float) AdBatVolt * 3.3f * 33 / 4095;
		CapVolt = (float) AdCapVolt * 3.3f * 33 / 4095;
		BatVoltFilt = BatVolt * 0.1f + BatVoltFilt * 0.9f;
		CapVoltFilt = CapVolt * 0.1f + CapVoltFilt * 0.9f;

		if ((g_sComFlag.bMainState == ON) && (g_sRunVar.iThrottleCmd == 0))
		{
			if (g_sRunVar.AdjustVoltSta == 0)
			{
				if (Cnt == 1000)
				{
					BatFactor = (float) g_sRunVar.iAdjustVolt * 100
							/ BatVoltFilt;
					CapFactor = (float) g_sRunVar.iAdjustVolt * 100
							/ CapVoltFilt;

					if ((9000 < BatFactor) && (BatFactor < 11000)
							&& (9000 < CapFactor) && (CapFactor < 11000))
					{
						g_uUserParm.iFactorBat = BatFactor;
						g_uUserParm.iFactorCap = CapFactor;
						EEWriteSta = WriteParm(252, BatFactor);
						EEWriteSta += WriteParm(253, CapFactor);
						if (EEWriteSta == 2)
						{
							g_sRunVar.AdjustVoltSta = 1;        //校准完成
						}
						else
						{
							g_sRunVar.AdjustVoltSta = 2;        //校准失败 参数写入失败
						}
					}
					else
					{
						g_sRunVar.AdjustVoltSta = 3;        //校准失败 校准系数超范围
					}
				}
			}
		}
		else
		{
			g_sRunVar.AdjustVoltSta = 5;        //校准失败 当前状态不能校准
		}

		if (Cnt % 5 == 1)
		{
			VOLTADJ_TX.MD01 = (uint16_t) (g_uAnalogValue.iBatVolt * 10);
			VOLTADJ_TX.MD23 = g_uAnalogValue.iCapacityVolt;
			if (g_sComFlag.bDragMode == DRAG_20)
			{
				VOLTADJ_TX.MD6 = g_sRunVar.AdjustVoltSta;
				VOLTADJ_TX.MD7 = g_sRunVar.cStartAdjustVolt;
			}
			else if (g_sComFlag.bDragMode == DRAG_60)
			{
				VOLTADJ_TX.MD4 = g_sRunVar.AdjustVoltSta;
				VOLTADJ_TX.MD5 = g_sRunVar.cStartAdjustVolt;
			}
			VOLTADJ_TX.Pending = 1;

//			Can_TEST_TX.MD0 = (uint16_t)(BatVoltFilt*1000);
//			Can_TEST_TX.MD1 = (uint16_t)(BatVoltFilt*1000)>>8;
//			Can_TEST_TX.MD2 = g_sRunVar.AdBat;
//			Can_TEST_TX.MD3 = g_sRunVar.AdBat>>8;
//			Can_TEST_TX.MD4 = AdBatVolt;
//			Can_TEST_TX.MD5 = AdBatVolt>>8;
//			Can_TEST_TX.MD6 = g_sRunVar.iAdjustVolt;
//			Can_TEST_TX.MD7 = g_sRunVar.iAdjustVolt>>8;
//			Can_TEST_TX.Pending = 1;
		}
	}
	if (g_sRunVar.cStartAdjustVolt == 2)
	{
		Cnt = 0;
	}
}

void CrntAdjust(void)
{
	uint16_t CrntFactor1 = 0;
	uint16_t CrntFactor2 = 0;
	static uint16_t Cnt = 0;
	uint8_t EEWriteSta = 0;

	if (g_sRunVar.cStartAdjustVolt == 3)
	{
		Cnt++;
		if (g_sComFlag.bMainState == ON)
		{
			if (g_sRunVar.AdjustVoltSta == 0)
			{
				if (Cnt == 100)
				{
					if ((g_sRunVar.iAdjustCrnt1 > 15000)
							&& (g_sRunVar.iAdjustCrnt2 > 15000))
					{
						CrntFactor1 = g_sRunVar.iCrntRmsFilt1 * 100
								/ g_sRunVar.iAdjustCrnt1
								* g_uUserParm.iFactorSensorU;
						CrntFactor2 = g_sRunVar.iCrntRmsFilt2 * 100
								/ g_sRunVar.iAdjustCrnt2
								* g_uUserParm.iFactorSensorV;
						if ((8000 <= CrntFactor1) && (CrntFactor1 <= 12000)
								&& (8000 <= CrntFactor2)
								&& (CrntFactor2 <= 12000))
						{
//							g_uUserParm.iFactorSensorU = CrntFactor1;
//							g_uUserParm.iFactorSensorV = CrntFactor2;
							EEWriteSta = WriteParm(254, CrntFactor1);
							EEWriteSta += WriteParm(255, CrntFactor2);
							if (EEWriteSta == 2)
							{
								g_sRunVar.AdjustVoltSta = 1;        //校准完成
							}
							else
							{
								g_sRunVar.AdjustVoltSta = 2;       //校准失败 参数写入失败
							}
						}
						else
						{
							g_sRunVar.AdjustVoltSta = 3;        //校准失败 校准系数超范围
						}
					}
					else
					{
						g_sRunVar.AdjustVoltSta = 4;        //校准失败 校准电流小
					}
				}
			}
		}
		else
		{
			g_sRunVar.AdjustVoltSta = 5;        //校准失败 当前状态不能校准
		}

		if (Cnt % 5 == 1)
		{
			VOLTADJ_TX.MD01 = g_sRunVar.iCrntRmsFilt1 * 100;
			VOLTADJ_TX.MD23 = g_sRunVar.iCrntRmsFilt2 * 100;
			if (g_sComFlag.bDragMode == DRAG_20)
			{
				VOLTADJ_TX.MD6 = g_sRunVar.AdjustVoltSta;
				VOLTADJ_TX.MD7 = g_sRunVar.cStartAdjustVolt;
			}
			else if (g_sComFlag.bDragMode == DRAG_60)
			{
				VOLTADJ_TX.MD4 = g_sRunVar.AdjustVoltSta;
				VOLTADJ_TX.MD5 = g_sRunVar.cStartAdjustVolt;
			}
			VOLTADJ_TX.Pending = 1;
		}
	}
	if (g_sRunVar.cStartAdjustVolt == 4)
	{
		Cnt = 0;
	}
}
void Adjust(void)
{
#if MODE == ECAR
	VoltAdjust();
	CrntAdjust();
#endif
}
