../AC_Motor_Control/MotorControl/Src/FOC_IM_V1.cpp:26:1:CFOC_IM_V1::CFOC_IM_V1()	8	static
../AC_Motor_Control/MotorControl/Src/FOC_IM_V1.cpp:49:6:void CFOC_IM_V1::MtrPrmsInit()	0	static
../AC_Motor_Control/MotorControl/Src/FOC_IM_V1.cpp:38:6:void CFOC_IM_V1::LoadMtrPrms(const FOCIMV1MtrPrms_t*)	8	static
../AC_Motor_Control/MotorControl/Src/FOC_IM_V1.cpp:86:6:void CFOC_IM_V1::IqRefCalc()	16	static
../AC_Motor_Control/MotorControl/Src/FOC_IM_V1.cpp:323:6:void setFOCIMV1Prms_t(const FOCIMV1Prms_t*)	4	static
../STM32G4_Drivers/Inc\Timer.h:58:6:void CTimer<CntType>::SetStart() [with CntType = short unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:71:9:CntType CTimer<CntType>::GetTimeElapsed(uint8_t) [with CntType = short unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:106:6:void CTimer<CntType>::SaveSmpToRec() [with CntType = short unsigned int]	0	static
../AC_Motor_Control/MotorControl/Src/..\Inc\../../Common/Inc/UserMaths.h:145:7:float CDifferential<InType>::GetVal() [with InType = float]	24	static
../AC_Motor_Control/MotorControl/Src/FOC_IM_V1.cpp:119:6:void CFOC_IM_V1::NorPrcs()	16	static
../AC_Motor_Control/MotorControl/Src/FOC_IM_V1.cpp:193:6:void CFOC_IM_V1::ShtDwnPrcs()	16	static
../AC_Motor_Control/MotorControl/Src/FOC_IM_V1.cpp:237:6:void CFOC_IM_V1::IdIqDrctCtl(float, float)	16	static
