../AC_Motor_Control/MotorControl/Src/FOC_IM.cpp:23:1:CFOC_IM::CFOC_IM()	16	static
../AC_Motor_Control/MotorControl/Src/FOC_IM.cpp:67:6:void CFOC_IM::MtrPrmsInit()	0	static
../AC_Motor_Control/MotorControl/Src/FOC_IM.cpp:57:6:void CFOC_IM::LoadMtrPrms(const FOCIMMtrPrms_t*)	8	static
../AC_Motor_Control/MotorControl/Src/FOC_IM.cpp:86:6:void CFOC_IM::IdLimitCalc()	8	static
../AC_Motor_Control/MotorControl/Src/FOC_IM.cpp:122:6:void CFOC_IM::ExcCrvCmpCalc()	0	static
../AC_Motor_Control/MotorControl/Src/FOC_IM.cpp:134:6:void CFOC_IM::CurrModelCalc()	16	static
../AC_Motor_Control/MotorControl/Src/FOC_IM.cpp:185:6:void CFOC_IM::IqRefCalc()	16	static
../AC_Motor_Control/MotorControl/Src/FOC_IM.cpp:968:6:void setFOCIMPrms_t(const FOCIMPrms_t*)	4	static
../STM32G4_Drivers/Inc\Timer.h:58:6:void CTimer<CntType>::SetStart() [with CntType = long unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:89:9:uint8_t CTimer<CntType>::CheckTimeElps(CntType) [with CntType = long unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:71:9:CntType CTimer<CntType>::GetTimeElapsed(uint8_t) [with CntType = long unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:58:6:void CTimer<CntType>::SetStart() [with CntType = short unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:71:9:CntType CTimer<CntType>::GetTimeElapsed(uint8_t) [with CntType = short unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:106:6:void CTimer<CntType>::SaveSmpToRec() [with CntType = short unsigned int]	0	static
../AC_Motor_Control/MotorControl/Src/..\Inc\../../Common/Inc/UserMaths.h:145:7:float CDifferential<InType>::GetVal() [with InType = float]	24	static
../AC_Motor_Control/MotorControl/Src/FOC_IM.cpp:263:6:void CFOC_IM::NorPrcs()	8	static
../AC_Motor_Control/MotorControl/Src/FOC_IM.cpp:337:6:void CFOC_IM::IdIqDrctCtl(float, float)	24	static
../AC_Motor_Control/MotorControl/Src/FOC_IM.cpp:410:6:void CFOC_IM::StdyPrcs(float, float, uint8_t)	24	static
../AC_Motor_Control/MotorControl/Src/FOC_IM.cpp:521:6:void CFOC_IM::ClbPrcs()	24	static
../AC_Motor_Control/MotorControl/Src/FOC_IM.cpp:468:6:void CFOC_IM::ShtDwnPrcs()	16	static
