################################################################################
# Automatically-generated file. Do not edit!
# Toolchain: GNU Tools for STM32 (13.3.rel1)
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
CPP_SRCS += \
../AC_Motor_Control/Common/Src/ADCProcess.cpp \
../AC_Motor_Control/Common/Src/DiagSCI.cpp \
../AC_Motor_Control/Common/Src/PstnSnsr.cpp \
../AC_Motor_Control/Common/Src/UserMaths.cpp 

OBJS += \
./AC_Motor_Control/Common/Src/ADCProcess.o \
./AC_Motor_Control/Common/Src/DiagSCI.o \
./AC_Motor_Control/Common/Src/PstnSnsr.o \
./AC_Motor_Control/Common/Src/UserMaths.o 

CPP_DEPS += \
./AC_Motor_Control/Common/Src/ADCProcess.d \
./AC_Motor_Control/Common/Src/DiagSCI.d \
./AC_Motor_Control/Common/Src/PstnSnsr.d \
./AC_Motor_Control/Common/Src/UserMaths.d 


# Each subdirectory must supply rules for building sources it contributes
AC_Motor_Control/Common/Src/%.o AC_Motor_Control/Common/Src/%.su AC_Motor_Control/Common/Src/%.cyclo: ../AC_Motor_Control/Common/Src/%.cpp AC_Motor_Control/Common/Src/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32G431xx -DUSE_HAL_DRIVER -DDEBUG -c -I../Core/Inc -I../Drivers/STM32G4xx_HAL_Driver/Inc -I../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I../Drivers/CMSIS/Include -I../AC_Motor_Control -I../STM32G4_Drivers -I../LogicControl/Inc -I../Devices -I../Modules/Inc -Og -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-threadsafe-statics -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"

clean: clean-AC_Motor_Control-2f-Common-2f-Src

clean-AC_Motor_Control-2f-Common-2f-Src:
	-$(RM) ./AC_Motor_Control/Common/Src/ADCProcess.cyclo ./AC_Motor_Control/Common/Src/ADCProcess.d ./AC_Motor_Control/Common/Src/ADCProcess.o ./AC_Motor_Control/Common/Src/ADCProcess.su ./AC_Motor_Control/Common/Src/DiagSCI.cyclo ./AC_Motor_Control/Common/Src/DiagSCI.d ./AC_Motor_Control/Common/Src/DiagSCI.o ./AC_Motor_Control/Common/Src/DiagSCI.su ./AC_Motor_Control/Common/Src/PstnSnsr.cyclo ./AC_Motor_Control/Common/Src/PstnSnsr.d ./AC_Motor_Control/Common/Src/PstnSnsr.o ./AC_Motor_Control/Common/Src/PstnSnsr.su ./AC_Motor_Control/Common/Src/UserMaths.cyclo ./AC_Motor_Control/Common/Src/UserMaths.d ./AC_Motor_Control/Common/Src/UserMaths.o ./AC_Motor_Control/Common/Src/UserMaths.su

.PHONY: clean-AC_Motor_Control-2f-Common-2f-Src

