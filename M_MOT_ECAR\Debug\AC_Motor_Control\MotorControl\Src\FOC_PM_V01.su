../AC_Motor_Control/MotorControl/Src/FOC_PM_V01.cpp:35:1:CFOC_PM_V01::CFOC_PM_V01()	16	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V01.cpp:83:6:void CFOC_PM_V01::MtrPrmsInit()	4	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V01.cpp:73:6:void CFOC_PM_V01::LoadMtrPrms(const FOCPMV01MtrPrms_t*)	8	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V01.cpp:165:7:float CFOC_PM_V01::MTPACalc(float)	0	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V01.cpp:174:6:void CFOC_PM_V01::Park()	16	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V01.cpp:731:6:void CFOC_PM_V01::SRMEcdrOfst()	16	static
../STM32G4_Drivers/Inc\Timer.h:58:6:void CTimer<CntType>::SetStart() [with CntType = long unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:89:9:uint8_t CTimer<CntType>::CheckTimeElps(CntType) [with CntType = long unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:71:9:CntType CTimer<CntType>::GetTimeElapsed(uint8_t) [with CntType = long unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:58:6:void CTimer<CntType>::SetStart() [with CntType = short unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:71:9:CntType CTimer<CntType>::GetTimeElapsed(uint8_t) [with CntType = short unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:106:6:void CTimer<CntType>::SaveSmpToRec() [with CntType = short unsigned int]	0	static
../AC_Motor_Control/MotorControl/Src/..\Inc\../../Common/Inc/UserMaths.h:145:7:float CDifferential<InType>::GetVal() [with InType = float]	24	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V01.cpp:196:6:void CFOC_PM_V01::NorPrcs()	24	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V01.cpp:693:6:void CFOC_PM_V01::IsTqMxTest()	8	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V01.cpp:706:6:void CFOC_PM_V01::TqLnTest()	8	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V01.cpp:907:6:void CFOC_PM_V01::ClbPrcs()	16	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V01.cpp:342:6:void CFOC_PM_V01::IdIqDrctCtl(float, float)	16	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V01.cpp:422:6:void CFOC_PM_V01::PssvEcdrOfst()	24	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V01.cpp:467:6:void CFOC_PM_V01::ActvEcdrOfst()	32	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V01.cpp:599:6:void CFOC_PM_V01::IfMTPATest()	24	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V01.cpp:615:6:void CFOC_PM_V01::EcdrCmpTest()	8	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V01.cpp:807:6:void CFOC_PM_V01::ShtDwnPrcs()	8	static
