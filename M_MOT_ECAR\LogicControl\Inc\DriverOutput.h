/*
 * DriverOutput.h
 *
 *  Created on: Dec 28, 2021
 *      Author: RS
 */

#ifndef INC_DRIVEROUTPUT_H_
#define INC_DRIVEROUTPUT_H_

#include "stdint.h"

#define PRECHARGE_OFF PrechgOff()
#define PRECHARGE_ON PrechgOn()

#ifdef __cplusplus
extern "C"
{
#endif

void PrechgOff(void);
void PrechgOn(void);
void PWMInitial(uint16_t Frq);
void Driver1Output(uint8_t sw, int16_t duty);

#ifdef __cplusplus
}
#endif


#endif /* INC_DRIVEROUTPUT_H_ */
