../AC_Motor_Control/MotorControl/Src/FOC_PM_V1.cpp:36:1:CFOC_PM_V1::CFOC_PM_V1()	1
../AC_Motor_Control/MotorControl/Src/FOC_PM_V1.cpp:69:6:void CFOC_PM_V1::MtrPrmsInit()	9
../AC_Motor_Control/MotorControl/Src/FOC_PM_V1.cpp:58:6:void CFOC_PM_V1::LoadMtrPrms(const FOCPMV1MtrPrms_t*)	1
../AC_Motor_Control/MotorControl/Src/FOC_PM_V1.cpp:175:6:void CFOC_PM_V1::KFldWknMaxCalc()	10
../AC_Motor_Control/MotorControl/Src/FOC_PM_V1.cpp:851:6:void setFOCPMV1Prms_t(const FOCPMV1Prms_t*)	2
../STM32G4_Drivers/Inc\Timer.h:58:6:void CTimer<CntType>::SetStart() [with CntType = long unsigned int]	1
../STM32G4_Drivers/Inc\Timer.h:89:9:uint8_t CTimer<CntType>::CheckTimeElps(CntType) [with CntType = long unsigned int]	2
../STM32G4_Drivers/Inc\Timer.h:71:9:CntType CTimer<CntType>::GetTimeElapsed(uint8_t) [with CntType = long unsigned int]	2
../STM32G4_Drivers/Inc\Timer.h:58:6:void CTimer<CntType>::SetStart() [with CntType = short unsigned int]	1
../STM32G4_Drivers/Inc\Timer.h:71:9:CntType CTimer<CntType>::GetTimeElapsed(uint8_t) [with CntType = short unsigned int]	2
../STM32G4_Drivers/Inc\Timer.h:106:6:void CTimer<CntType>::SaveSmpToRec() [with CntType = short unsigned int]	1
../AC_Motor_Control/MotorControl/Src/..\Inc\../../Common/Inc/UserMaths.h:145:7:float CDifferential<InType>::GetVal() [with InType = float]	8
../AC_Motor_Control/MotorControl/Src/FOC_PM_V1.cpp:237:6:void CFOC_PM_V1::NorPrcs()	11
../AC_Motor_Control/MotorControl/Src/FOC_PM_V1.cpp:350:6:void CFOC_PM_V1::IdIqDrctCtl(float, float)	7
../AC_Motor_Control/MotorControl/Src/FOC_PM_V1.cpp:431:6:void CFOC_PM_V1::PssvEcdrOfst()	16
../AC_Motor_Control/MotorControl/Src/FOC_PM_V1.cpp:582:6:void CFOC_PM_V1::ActvEcdrOfst()	27
../AC_Motor_Control/MotorControl/Src/FOC_PM_V1.cpp:736:6:void CFOC_PM_V1::ShtDwnPrcs()	8
../AC_Motor_Control/MotorControl/Src/FOC_PM_V1.cpp:823:6:void CFOC_PM_V1::ClbPrcs()	3
