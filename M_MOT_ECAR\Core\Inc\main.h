/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.h
  * @brief          : Header for main.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; Copyright (c) 2020 STMicroelectronics.
  * All rights reserved.</center></h2>
  *
  * This software component is licensed by ST under BSD 3-Clause license,
  * the "License"; You may not use this file except in compliance with the
  * License. You may obtain a copy of the License at:
  *                        opensource.org/licenses/BSD-3-Clause
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32g4xx_hal.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */

/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
void Error_Handler(void);

/* USER CODE BEGIN EFP */

/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/
#define LED1_Pin GPIO_PIN_13
#define LED1_GPIO_Port GPIOC
#define DI3_MCU_Pin GPIO_PIN_14
#define DI3_MCU_GPIO_Port GPIOC
#define DI2_MCU_Pin GPIO_PIN_15
#define DI2_MCU_GPIO_Port GPIOC
#define Throttle_Volt_Pin GPIO_PIN_1
#define Throttle_Volt_GPIO_Port GPIOC
#define BUS_Volt_Pin GPIO_PIN_2
#define BUS_Volt_GPIO_Port GPIOC
#define Switch_DI11_Pin GPIO_PIN_3
#define Switch_DI11_GPIO_Port GPIOC
#define KSI_VOLT_Pin GPIO_PIN_0
#define KSI_VOLT_GPIO_Port GPIOA
#define MOT_TEMP_Pin GPIO_PIN_1
#define MOT_TEMP_GPIO_Port GPIOA
#define IU_Pin GPIO_PIN_2
#define IU_GPIO_Port GPIOA
#define Switch_DI15_Pin GPIO_PIN_3
#define Switch_DI15_GPIO_Port GPIOA
#define ConTemp_Pin GPIO_PIN_4
#define ConTemp_GPIO_Port GPIOA
#define OUTA_Pin GPIO_PIN_5
#define OUTA_GPIO_Port GPIOA
#define IV_Pin GPIO_PIN_6
#define IV_GPIO_Port GPIOA
#define LOGIC_VOLT_Pin GPIO_PIN_7
#define LOGIC_VOLT_GPIO_Port GPIOA
#define Status_NMC_Pin GPIO_PIN_4
#define Status_NMC_GPIO_Port GPIOC
#define RDC_RST_Pin GPIO_PIN_5
#define RDC_RST_GPIO_Port GPIOC
#define PWM_NMC_Pin GPIO_PIN_0
#define PWM_NMC_GPIO_Port GPIOB
#define DI1_MCU_Pin GPIO_PIN_1
#define DI1_MCU_GPIO_Port GPIOB
#define Curise_Switch_Pin GPIO_PIN_2
#define Curise_Switch_GPIO_Port GPIOB
#define UL_MCU_Pin GPIO_PIN_13
#define UL_MCU_GPIO_Port GPIOB
#define VL_MCU_Pin GPIO_PIN_14
#define VL_MCU_GPIO_Port GPIOB
#define WL_MCH_Pin GPIO_PIN_15
#define WL_MCH_GPIO_Port GPIOB
#define RDC_CS_Pin GPIO_PIN_6
#define RDC_CS_GPIO_Port GPIOC
#define Wake_Up_Pin GPIO_PIN_7
#define Wake_Up_GPIO_Port GPIOC
#define I2C3_SCL_Pin GPIO_PIN_8
#define I2C3_SCL_GPIO_Port GPIOC
#define UH_MCU_Pin GPIO_PIN_8
#define UH_MCU_GPIO_Port GPIOA
#define VH_MCU_Pin GPIO_PIN_9
#define VH_MCU_GPIO_Port GPIOA
#define WH_MCU_Pin GPIO_PIN_10
#define WH_MCU_GPIO_Port GPIOA
#define CAN0_RX_Pin GPIO_PIN_11
#define CAN0_RX_GPIO_Port GPIOA
#define CAN0_TX_Pin GPIO_PIN_12
#define CAN0_TX_GPIO_Port GPIOA
#define SWD_DIO_Pin GPIO_PIN_13
#define SWD_DIO_GPIO_Port GPIOA
#define SWD_CLK_Pin GPIO_PIN_14
#define SWD_CLK_GPIO_Port GPIOA
#define OUTZ_Pin GPIO_PIN_15
#define OUTZ_GPIO_Port GPIOA
#define RDC_SCK_Pin GPIO_PIN_10
#define RDC_SCK_GPIO_Port GPIOC
#define RDC_DATA_Pin GPIO_PIN_11
#define RDC_DATA_GPIO_Port GPIOC
#define RDC_RD_Pin GPIO_PIN_12
#define RDC_RD_GPIO_Port GPIOC
#define S_2_Pin GPIO_PIN_2
#define S_2_GPIO_Port GPIOD
#define OUTB_Pin GPIO_PIN_3
#define OUTB_GPIO_Port GPIOB
#define RDC_SAMPLE_Pin GPIO_PIN_5
#define RDC_SAMPLE_GPIO_Port GPIOB
#define S_0_Pin GPIO_PIN_6
#define S_0_GPIO_Port GPIOB
#define E_5V_CTRL_Pin GPIO_PIN_7
#define E_5V_CTRL_GPIO_Port GPIOB
#define S_1_Pin GPIO_PIN_9
#define S_1_GPIO_Port GPIOB
/* USER CODE BEGIN Private defines */
#define RLED_Pin LED1_Pin
#define RLED_GPIO_Port LED1_GPIO_Port
#define GLED_Pin LED1_Pin
#define GLED_GPIO_Port LED1_GPIO_Port

#define RLED_ON		HAL_GPIO_WritePin(RLED_GPIO_Port, RLED_Pin,0)
#define RLED_OFF	HAL_GPIO_WritePin(RLED_GPIO_Port, RLED_Pin,1)
#define RLED_TOG	HAL_GPIO_TogglePin(RLED_GPIO_Port, RLED_Pin)

#define GLED_ON		HAL_GPIO_WritePin(GLED_GPIO_Port, GLED_Pin,0)
#define GLED_OFF	HAL_GPIO_WritePin(GLED_GPIO_Port, GLED_Pin,1)
#define GLED_TOG	HAL_GPIO_TogglePin(GLED_GPIO_Port, GLED_Pin)

#define PEB_ENABLE		HAL_GPIO_WritePin(PEB_EN_MCU_GPIO_Port, PEB_EN_MCU_Pin,1)
#define PEB_DISABLE		HAL_GPIO_WritePin(PEB_EN_MCU_GPIO_Port, PEB_EN_MCU_Pin,0)

#define  WAKEUP_ENABLE  HAL_GPIO_WritePin(Wake_Up_GPIO_Port, Wake_Up_Pin,0)

#define SYSCLK HAL_RCC_GetSysClockFreq() // Hz



#define MAIN_PERIOD 0.002f
#define FCYCLE 1/MAIN_PERIOD

#define DRIVER1_STATE  HAL_GPIO_ReadPin(Status_NMC_GPIO_Port, Status_NMC_Pin)
#define DRIVER2_STATE  HAL_GPIO_ReadPin(Status_NMC_GPIO_Port, Status_NMC_Pin)
#define DRIVER3_STATE  HAL_GPIO_ReadPin(Status_NMC_GPIO_Port, Status_NMC_Pin)
#define DRIVER4_STATE  HAL_GPIO_ReadPin(Status_NMC_GPIO_Port, Status_NMC_Pin)
#define DRIVER5_STATE  HAL_GPIO_ReadPin(Status_NMC_GPIO_Port, Status_NMC_Pin)
#define DRIVER6_STATE  HAL_GPIO_ReadPin(Status_NMC_GPIO_Port, Status_NMC_Pin)

#define SW1_STATE	HAL_GPIO_ReadPin(DI1_MCU_GPIO_Port, DI1_MCU_Pin)	//PIN33
#define SW2_STATE	HAL_GPIO_ReadPin(DI2_MCU_GPIO_Port, DI2_MCU_Pin)	//PIN16
#define SW3_STATE	HAL_GPIO_ReadPin(DI3_MCU_GPIO_Port, DI3_MCU_Pin)	//PIN21

#define SW4_STATE	HAL_GPIO_ReadPin(DI3_MCU_GPIO_Port, DI3_MCU_Pin)	//PIN8
#define SW5_STATE	HAL_GPIO_ReadPin(DI3_MCU_GPIO_Port, DI3_MCU_Pin)	//PIN19
#define SW6_STATE	HAL_GPIO_ReadPin(DI3_MCU_GPIO_Port, DI3_MCU_Pin)	//PIN2
#define SW7_STATE	HAL_GPIO_ReadPin(DI3_MCU_GPIO_Port, DI3_MCU_Pin)	//PIN11
#define SW8_STATE	HAL_GPIO_ReadPin(DI3_MCU_GPIO_Port, DI3_MCU_Pin)	//PIN12
#define SW9_STATE	HAL_GPIO_ReadPin(DI3_MCU_GPIO_Port, DI3_MCU_Pin)	//PIN14
#define SW10_STATE	HAL_GPIO_ReadPin(DI3_MCU_GPIO_Port, DI3_MCU_Pin)	//PIN22
#define SW11_STATE	HAL_GPIO_ReadPin(DI3_MCU_GPIO_Port, DI3_MCU_Pin)	//PIN23

#define S0_CTRL(n)		HAL_GPIO_WritePin(S_0_GPIO_Port, S_0_Pin, n)
#define S1_CTRL(n)		HAL_GPIO_WritePin(S_1_GPIO_Port, S_0_Pin, n)
#define S2_CTRL(n)		HAL_GPIO_WritePin(S_2_GPIO_Port, S_0_Pin, n)

#define A0_STATE	HAL_GPIO_ReadPin(Curise_Switch_GPIO_Port, Curise_Switch_Pin)	//PIN22

#define BRIDGE_ON   //HAL_GPIO_WritePin(SHUTDOWN_GPIO_Port, SHUTDOWN_Pin,1)
#define BRIDGE_OFF  //HAL_GPIO_WritePin(SHUTDOWN_GPIO_Port, SHUTDOWN_Pin,0)

/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */
