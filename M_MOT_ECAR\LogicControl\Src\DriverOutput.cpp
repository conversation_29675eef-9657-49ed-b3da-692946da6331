/*
 * DriverOut.c
 *
 *  Created on: Dec 28, 2021
 *      Author: RS
 */
#include "DriverOutput.h"
#include "Inc\PWM.h"

CPWM PWM1(1);//PIN26-NLC


void PWMInitial(uint16_t Frq)
{
	PWM1.SetFreq(Frq);
	PWM1.StartCnt();
	PWM1.OutputOff();
}

void PrechgOff(void)
{
//	PreChg.OutputOff();
}
void PrechgOn(void)
{
//	PreChg.OutputOn();
}
void Driver1Output(uint8_t sw, int16_t duty)//PIN26-NLC
{
	if(sw)
	{
		PWM1.OutputOn();
	}
	else
	{
		PWM1.OutputOff();
	}
	PWM1.SetDtyCcl((float)duty/1000);
}
