../STM32G4_Drivers/Src/DMA.cpp:13:1:CDMACh::CDMACh()	1
../STM32G4_Drivers/Src/DMA.cpp:21:6:void CDMACh::SelectCh()	13
../STM32G4_Drivers/Src/DMA.cpp:81:6:void CDMACh::SelectCh(DMA_Channel_TypeDef*)	1
../STM32G4_Drivers/Src/DMA.cpp:102:6:void CDMACh::Enable()	1
../STM32G4_Drivers/Src/DMA.cpp:110:6:void CDMACh::Disable()	1
../STM32G4_Drivers/Src/DMA.cpp:118:6:void CDMACh::Start()	1
../STM32G4_Drivers/Src/DMA.cpp:127:9:uint8_t CDMACh::ChkTrnsFin()	1
../STM32G4_Drivers/Src/DMA.cpp:136:6:void CDMACh::SetNDTR(uint16_t)	1
../STM32G4_Drivers/Src/DMA.cpp:144:10:uint16_t CDMACh::GetNDTR()	1
../STM32G4_Drivers/Src/DMA.cpp:153:6:void CDMACh::SetPSize(uint8_t)	4
../STM32G4_Drivers/Src/DMA.cpp:176:6:void CDMACh::SetMSize(uint8_t)	4
../STM32G4_Drivers/Src/DMA.cpp:197:6:void CDMACh::SetCirMode(uint8_t)	2
../STM32G4_Drivers/Src/DMA.cpp:226:6:void CDMACh::SetBffr0Addr(uint32_t)	1
../STM32G4_Drivers/Src/DMA.cpp:242:6:void CDMACh::SetPrphrlAddr(uint32_t)	1
../STM32G4_Drivers/Src/DMA.cpp:251:6:void CDMACh::SetMINC(uint8_t)	2
../STM32G4_Drivers/Src/DMA.cpp:398:9:uint8_t CDMACh::Transmit(uint16_t, uint32_t)	5
