../AC_Motor_Control/Common/Src/PstnSnsr.cpp:20:1:CPstnSnsr::CPstnSnsr()	0	static
../AC_Motor_Control/Common/Src/PstnSnsr.cpp:31:1:CPstnSnsr_QEP::CPstnSnsr_QEP()	8	static
../AC_Motor_Control/Common/Src/PstnSnsr.cpp:47:6:void CPstnSnsr_QEP::Init(CFOC*)	8	static
../AC_Motor_Control/Common/Src/PstnSnsr.cpp:83:11:PrcsStt_t CPstnSnsr_QEP::DvcInit()	0	static
../AC_Motor_Control/Common/Src/PstnSnsr.cpp:92:6:void CPstnSnsr_QEP::Read()	16	static
../AC_Motor_Control/Common/Src/PstnSnsr.cpp:103:6:void CPstnSnsr_QEP::PostRead()	16	static
../AC_Motor_Control/Common/Src/PstnSnsr.cpp:117:10:uint32_t CPstnSnsr_QEP::Monitor()	0	static
../AC_Motor_Control/Common/Src/PstnSnsr.cpp:126:9:uint8_t CPstnSnsr_QEP::GetRttDir()	8	static
../AC_Motor_Control/Common/Src/PstnSnsr.cpp:246:1:CPstnSnsr_RT::CPstnSnsr_RT()	8	static
../AC_Motor_Control/Common/Src/PstnSnsr.cpp:400:6:void CPstnSnsr_RT_SPI::Init(CFOC*)	0	static
../AC_Motor_Control/Common/Src/PstnSnsr.cpp:430:6:void CPstnSnsr_RT_SPI::Read()	8	static
../AC_Motor_Control/Common/Src/PstnSnsr.cpp:441:6:void CPstnSnsr_RT_SPI::PostRead()	8	static
../STM32G4_Drivers/Inc\Timer.h:58:6:void CTimer<CntType>::SetStart() [with CntType = long unsigned int]	0	static
../AC_Motor_Control/Common/Src/PstnSnsr.cpp:263:11:PrcsStt_t CPstnSnsr_RT::DvcInit()	24	static
../AC_Motor_Control/Common/Src/PstnSnsr.cpp:408:11:PrcsStt_t CPstnSnsr_RT_SPI::DvcInit()	16	static
../STM32G4_Drivers/Inc\Timer.h:89:9:uint8_t CTimer<CntType>::CheckTimeElps(CntType) [with CntType = long unsigned int]	0	static
../AC_Motor_Control/Common/Src/PstnSnsr.cpp:319:10:uint32_t CPstnSnsr_RT::Monitor()	16	static
../AC_Motor_Control/Common/Src/PstnSnsr.cpp:455:10:uint32_t CPstnSnsr_RT_SPI::Monitor()	8	static
