../AC_Motor_Control/MotorControl/Src/FOC_BLDC.cpp:44:1:CFOC_BLDC::CFOC_BLDC()	8	static
../AC_Motor_Control/MotorControl/Src/FOC_BLDC.cpp:73:6:void CFOC_BLDC::LoadMtrPrms(const FOCBLDCMtrPrms_t*)	0	static
../AC_Motor_Control/MotorControl/Src/FOC_BLDC.cpp:98:7:float CFOC_BLDC::GetHallAngl()	0	static
../AC_Motor_Control/MotorControl/Src/FOC_BLDC.cpp:108:7:float CFOC_BLDC::GetHallTrnsAngl(float, float)	8	static
../AC_Motor_Control/MotorControl/Src/FOC_BLDC.cpp:124:7:float CFOC_BLDC::GetAnglDiff(float, float)	0	static
../AC_Motor_Control/MotorControl/Src/FOC_BLDC.cpp:144:6:void CFOC_BLDC::AnglSlope(float*, float, float)	32	static
../AC_Motor_Control/MotorControl/Src/FOC_BLDC.cpp:180:6:void CFOC_BLDC::AnglSpdCalc()	32	static
../AC_Motor_Control/MotorControl/Src/FOC_BLDC.cpp:493:6:void CFOC_BLDC::NorPrcs()	16	static
../AC_Motor_Control/MotorControl/Src/FOC_BLDC.cpp:570:6:void CFOC_BLDC::ShtDwnPrcs()	16	static
../AC_Motor_Control/MotorControl/Src/FOC_BLDC.cpp:621:6:void CFOC_BLDC::ClbPrcs()	8	static
../AC_Motor_Control/MotorControl/Src/FOC_BLDC.cpp:673:6:void CFOC_BLDC::IdIqDrctCtl(float, float)	16	static
../AC_Motor_Control/MotorControl/Src/FOC_BLDC.cpp:710:6:void setFOCBLDCPrms_t(const FOCBLDCPrms_t*)	4	static
