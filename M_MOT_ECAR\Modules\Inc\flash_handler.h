/**
 * @file flash_handler.h
 * @brief Flash操作处理模块头文件
 */

#ifndef __FLASH_HANDLER_H
#define __FLASH_HANDLER_H

#ifdef __cplusplus
extern "C"
{
#endif

/* 包含所需头文件 */
#include "stm32g4xx_hal.h"
#include <stdbool.h>
#include <stdint.h>
    // #define APP_VALID_FLAG_ADDRESS (0x0801FFF8)
    // #define APP_VALID_MAGIC_NUMBER (0x12345678)
    //
    //// Bootloader 请求标志位 (存储在 Flash 特定地址)
    // #define BOOTLOADER_FLAG_ADDRESS (0x0801FFFC)
    // #define BOOTLOADER_REQUEST_MAGIC (0x87654321)

    /* 函数声明 */
    bool flash_update_bootloader_flags(void);
    HAL_StatusTypeDef FLASH_WriteTestData(void);
    bool flash_write_app_valid_flag(uint32_t address, uint32_t value);
    HAL_StatusTypeDef FLASH_ErasePages(uint32_t startAddress, uint32_t numPages);

    /**
     * @brief 同时更新 Bootloader 请求标志和 App 有效标志 (使用 Read-Modify-Erase-Write)
     * @retval true 如果更新成功, false 如果失败
     */
    bool flash_update_bootloader_flags(void);

#ifdef __cplusplus
}
#endif

#endif /* __FLASH_HANDLER_H */
