../AC_Motor_Control/Common/Src/UserMaths.cpp:25:6:void CLPF_1Ord::Process(float)	0	static
../AC_Motor_Control/Common/Src/UserMaths.cpp:16:6:void CLPF_1Ord::Process()	8	static
../AC_Motor_Control/Common/Src/UserMaths.cpp:44:6:void CLPF_1Ord::SetIntlVal()	0	static
../AC_Motor_Control/Common/Src/UserMaths.cpp:52:6:void CLPF_1Ord::SetIntlVal(float)	0	static
../AC_Motor_Control/Common/Src/UserMaths.cpp:87:7:float CTable_1Dim::Lookup(float)	0	static
../AC_Motor_Control/Common/Src/UserMaths.cpp:121:7:float CTable_1Dim::InvLookup(float)	24	static
../AC_Motor_Control/Common/Src/UserMaths.cpp:218:7:float CTable_2Dim::Lookup(float, float)	4	static
../AC_Motor_Control/Common/Src/UserMaths.cpp:268:1:CNTC::CNTC(float, float)	0	static
../AC_Motor_Control/Common/Src/UserMaths.cpp:277:7:float CNTC::Calc(float)	8	static
../AC_Motor_Control/Common/Src/UserMaths.cpp:286:1:CNTC_PllUp::CNTC_PllUp(float, float, float, float)	16	static
../AC_Motor_Control/Common/Src/UserMaths.cpp:296:7:float CNTC_PllUp::GetTemp(float)	8	static
../AC_Motor_Control/Common/Src/UserMaths.cpp:307:7:float CNTC_PllUp::GetTemp()	8	static
../AC_Motor_Control/Common/Src/UserMaths.cpp:345:7:float satf(float, float, float)	0	static
../AC_Motor_Control/Common/Src/UserMaths.cpp:60:6:void CPIReg::Process()	32	static
../AC_Motor_Control/Common/Src/UserMaths.cpp:319:7:float CPT100_0::Calc(float)	24	static
../AC_Motor_Control/Common/Src/UserMaths.cpp:328:7:float CPT100::GetTemp(float)	8	static
../AC_Motor_Control/Common/Src/UserMaths.cpp:336:7:float CPT1000::GetTemp(float)	8	static
../AC_Motor_Control/Common/Src/UserMaths.cpp:363:9:int32_t sat(int32_t, int32_t, int32_t)	0	static
../AC_Motor_Control/Common/Src/UserMaths.cpp:382:7:float AglNrmlz(float)	0	static
../AC_Motor_Control/Common/Src/UserMaths.cpp:396:7:float NTCCalc(float, float)	16	static
../AC_Motor_Control/Common/Src/UserMaths.cpp:404:7:float PT100_0Calc(float)	24	static
../AC_Motor_Control/Common/Src/UserMaths.cpp:414:7:float PolynmCalc(float, uint8_t, float*)	0	static
../AC_Motor_Control/Common/Src/UserMaths.cpp:435:10:uint16_t CRC16Calc(void*, uint16_t, uint16_t)	4	static
../AC_Motor_Control/Common/Src/UserMaths.cpp:461:10:uint32_t CRC32Calc(void*, uint16_t, uint32_t)	4	static
../AC_Motor_Control/Common/Src/UserMaths.cpp:485:9:int32_t GetFraction(float_t)	0	static
../AC_Motor_Control/Common/Src/UserMaths.cpp:491:9:float_t GetVectMod(float_t, float_t)	8	static
