../AC_Motor_Control/MotorControl/Src/FOC_PM_V11.cpp:33:1:CFOC_PM_V11::CFOC_PM_V11()	1
../AC_Motor_Control/MotorControl/Src/FOC_PM_V11.cpp:68:6:void CFOC_PM_V11::MtrPrmsInit()	9
../AC_Motor_Control/MotorControl/Src/FOC_PM_V11.cpp:57:6:void CFOC_PM_V11::LoadMtrPrms(const FOCPMV11MtrPrms_t*)	1
../AC_Motor_Control/MotorControl/Src/FOC_PM_V11.cpp:169:6:void CFOC_PM_V11::KFldWknMaxCalc()	10
../AC_Motor_Control/MotorControl/Src/FOC_PM_V11.cpp:231:6:void CFOC_PM_V11::Park()	1
../STM32G4_Drivers/Inc\Timer.h:58:6:void CTimer<CntType>::SetStart() [with CntType = long unsigned int]	1
../STM32G4_Drivers/Inc\Timer.h:89:9:uint8_t CTimer<CntType>::CheckTimeElps(CntType) [with CntType = long unsigned int]	2
../STM32G4_Drivers/Inc\Timer.h:71:9:CntType CTimer<CntType>::GetTimeElapsed(uint8_t) [with CntType = long unsigned int]	2
../STM32G4_Drivers/Inc\Timer.h:58:6:void CTimer<CntType>::SetStart() [with CntType = short unsigned int]	1
../STM32G4_Drivers/Inc\Timer.h:71:9:CntType CTimer<CntType>::GetTimeElapsed(uint8_t) [with CntType = short unsigned int]	2
../STM32G4_Drivers/Inc\Timer.h:106:6:void CTimer<CntType>::SaveSmpToRec() [with CntType = short unsigned int]	1
../AC_Motor_Control/MotorControl/Src/..\Inc\../../Common/Inc/UserMaths.h:145:7:float CDifferential<InType>::GetVal() [with InType = float]	8
../AC_Motor_Control/MotorControl/Src/FOC_PM_V11.cpp:253:6:void CFOC_PM_V11::NorPrcs()	10
../AC_Motor_Control/MotorControl/Src/FOC_PM_V11.cpp:462:6:void CFOC_PM_V11::IdIqDrctCtl(float, float)	6
../AC_Motor_Control/MotorControl/Src/FOC_PM_V11.cpp:533:6:void CFOC_PM_V11::PssvEcdrOfst()	16
../AC_Motor_Control/MotorControl/Src/FOC_PM_V11.cpp:683:6:void CFOC_PM_V11::ActvEcdrOfst()	22
../AC_Motor_Control/MotorControl/Src/FOC_PM_V11.cpp:814:6:void CFOC_PM_V11::ShtDwnPrcs()	8
../AC_Motor_Control/MotorControl/Src/FOC_PM_V11.cpp:901:6:void CFOC_PM_V11::ClbPrcs()	3
