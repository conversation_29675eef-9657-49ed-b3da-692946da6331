################################################################################
# Automatically-generated file. Do not edit!
# Toolchain: GNU Tools for STM32 (13.3.rel1)
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
CPP_SRCS += \
../AC_Motor_Control/MotorControl/Src/DCMtrCtrl.cpp \
../AC_Motor_Control/MotorControl/Src/DrvMtrCtrl.cpp \
../AC_Motor_Control/MotorControl/Src/FOC_BLDC.cpp \
../AC_Motor_Control/MotorControl/Src/FOC_Base.cpp \
../AC_Motor_Control/MotorControl/Src/FOC_IM.cpp \
../AC_Motor_Control/MotorControl/Src/FOC_IM_V1.cpp \
../AC_Motor_Control/MotorControl/Src/FOC_IM_V2.cpp \
../AC_Motor_Control/MotorControl/Src/FOC_PM_V0.cpp \
../AC_Motor_Control/MotorControl/Src/FOC_PM_V01.cpp \
../AC_Motor_Control/MotorControl/Src/FOC_PM_V1.cpp \
../AC_Motor_Control/MotorControl/Src/FOC_PM_V11.cpp \
../AC_Motor_Control/MotorControl/Src/FOC_PM_V2.cpp \
../AC_Motor_Control/MotorControl/Src/FOC_SPMSM_Sensorless.cpp \
../AC_Motor_Control/MotorControl/Src/MotorControl.cpp \
../AC_Motor_Control/MotorControl/Src/ShrtctOpnTst.cpp 

OBJS += \
./AC_Motor_Control/MotorControl/Src/DCMtrCtrl.o \
./AC_Motor_Control/MotorControl/Src/DrvMtrCtrl.o \
./AC_Motor_Control/MotorControl/Src/FOC_BLDC.o \
./AC_Motor_Control/MotorControl/Src/FOC_Base.o \
./AC_Motor_Control/MotorControl/Src/FOC_IM.o \
./AC_Motor_Control/MotorControl/Src/FOC_IM_V1.o \
./AC_Motor_Control/MotorControl/Src/FOC_IM_V2.o \
./AC_Motor_Control/MotorControl/Src/FOC_PM_V0.o \
./AC_Motor_Control/MotorControl/Src/FOC_PM_V01.o \
./AC_Motor_Control/MotorControl/Src/FOC_PM_V1.o \
./AC_Motor_Control/MotorControl/Src/FOC_PM_V11.o \
./AC_Motor_Control/MotorControl/Src/FOC_PM_V2.o \
./AC_Motor_Control/MotorControl/Src/FOC_SPMSM_Sensorless.o \
./AC_Motor_Control/MotorControl/Src/MotorControl.o \
./AC_Motor_Control/MotorControl/Src/ShrtctOpnTst.o 

CPP_DEPS += \
./AC_Motor_Control/MotorControl/Src/DCMtrCtrl.d \
./AC_Motor_Control/MotorControl/Src/DrvMtrCtrl.d \
./AC_Motor_Control/MotorControl/Src/FOC_BLDC.d \
./AC_Motor_Control/MotorControl/Src/FOC_Base.d \
./AC_Motor_Control/MotorControl/Src/FOC_IM.d \
./AC_Motor_Control/MotorControl/Src/FOC_IM_V1.d \
./AC_Motor_Control/MotorControl/Src/FOC_IM_V2.d \
./AC_Motor_Control/MotorControl/Src/FOC_PM_V0.d \
./AC_Motor_Control/MotorControl/Src/FOC_PM_V01.d \
./AC_Motor_Control/MotorControl/Src/FOC_PM_V1.d \
./AC_Motor_Control/MotorControl/Src/FOC_PM_V11.d \
./AC_Motor_Control/MotorControl/Src/FOC_PM_V2.d \
./AC_Motor_Control/MotorControl/Src/FOC_SPMSM_Sensorless.d \
./AC_Motor_Control/MotorControl/Src/MotorControl.d \
./AC_Motor_Control/MotorControl/Src/ShrtctOpnTst.d 


# Each subdirectory must supply rules for building sources it contributes
AC_Motor_Control/MotorControl/Src/%.o AC_Motor_Control/MotorControl/Src/%.su AC_Motor_Control/MotorControl/Src/%.cyclo: ../AC_Motor_Control/MotorControl/Src/%.cpp AC_Motor_Control/MotorControl/Src/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32G431xx -DUSE_HAL_DRIVER -DDEBUG -c -I../Core/Inc -I../Drivers/STM32G4xx_HAL_Driver/Inc -I../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I../Drivers/CMSIS/Include -I../AC_Motor_Control -I../STM32G4_Drivers -I../LogicControl/Inc -I../Devices -I../Modules/Inc -Og -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-threadsafe-statics -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"

clean: clean-AC_Motor_Control-2f-MotorControl-2f-Src

clean-AC_Motor_Control-2f-MotorControl-2f-Src:
	-$(RM) ./AC_Motor_Control/MotorControl/Src/DCMtrCtrl.cyclo ./AC_Motor_Control/MotorControl/Src/DCMtrCtrl.d ./AC_Motor_Control/MotorControl/Src/DCMtrCtrl.o ./AC_Motor_Control/MotorControl/Src/DCMtrCtrl.su ./AC_Motor_Control/MotorControl/Src/DrvMtrCtrl.cyclo ./AC_Motor_Control/MotorControl/Src/DrvMtrCtrl.d ./AC_Motor_Control/MotorControl/Src/DrvMtrCtrl.o ./AC_Motor_Control/MotorControl/Src/DrvMtrCtrl.su ./AC_Motor_Control/MotorControl/Src/FOC_BLDC.cyclo ./AC_Motor_Control/MotorControl/Src/FOC_BLDC.d ./AC_Motor_Control/MotorControl/Src/FOC_BLDC.o ./AC_Motor_Control/MotorControl/Src/FOC_BLDC.su ./AC_Motor_Control/MotorControl/Src/FOC_Base.cyclo ./AC_Motor_Control/MotorControl/Src/FOC_Base.d ./AC_Motor_Control/MotorControl/Src/FOC_Base.o ./AC_Motor_Control/MotorControl/Src/FOC_Base.su ./AC_Motor_Control/MotorControl/Src/FOC_IM.cyclo ./AC_Motor_Control/MotorControl/Src/FOC_IM.d ./AC_Motor_Control/MotorControl/Src/FOC_IM.o ./AC_Motor_Control/MotorControl/Src/FOC_IM.su ./AC_Motor_Control/MotorControl/Src/FOC_IM_V1.cyclo ./AC_Motor_Control/MotorControl/Src/FOC_IM_V1.d ./AC_Motor_Control/MotorControl/Src/FOC_IM_V1.o ./AC_Motor_Control/MotorControl/Src/FOC_IM_V1.su ./AC_Motor_Control/MotorControl/Src/FOC_IM_V2.cyclo ./AC_Motor_Control/MotorControl/Src/FOC_IM_V2.d ./AC_Motor_Control/MotorControl/Src/FOC_IM_V2.o ./AC_Motor_Control/MotorControl/Src/FOC_IM_V2.su ./AC_Motor_Control/MotorControl/Src/FOC_PM_V0.cyclo ./AC_Motor_Control/MotorControl/Src/FOC_PM_V0.d ./AC_Motor_Control/MotorControl/Src/FOC_PM_V0.o ./AC_Motor_Control/MotorControl/Src/FOC_PM_V0.su ./AC_Motor_Control/MotorControl/Src/FOC_PM_V01.cyclo ./AC_Motor_Control/MotorControl/Src/FOC_PM_V01.d ./AC_Motor_Control/MotorControl/Src/FOC_PM_V01.o ./AC_Motor_Control/MotorControl/Src/FOC_PM_V01.su ./AC_Motor_Control/MotorControl/Src/FOC_PM_V1.cyclo ./AC_Motor_Control/MotorControl/Src/FOC_PM_V1.d ./AC_Motor_Control/MotorControl/Src/FOC_PM_V1.o ./AC_Motor_Control/MotorControl/Src/FOC_PM_V1.su ./AC_Motor_Control/MotorControl/Src/FOC_PM_V11.cyclo ./AC_Motor_Control/MotorControl/Src/FOC_PM_V11.d ./AC_Motor_Control/MotorControl/Src/FOC_PM_V11.o ./AC_Motor_Control/MotorControl/Src/FOC_PM_V11.su ./AC_Motor_Control/MotorControl/Src/FOC_PM_V2.cyclo ./AC_Motor_Control/MotorControl/Src/FOC_PM_V2.d ./AC_Motor_Control/MotorControl/Src/FOC_PM_V2.o ./AC_Motor_Control/MotorControl/Src/FOC_PM_V2.su ./AC_Motor_Control/MotorControl/Src/FOC_SPMSM_Sensorless.cyclo ./AC_Motor_Control/MotorControl/Src/FOC_SPMSM_Sensorless.d ./AC_Motor_Control/MotorControl/Src/FOC_SPMSM_Sensorless.o ./AC_Motor_Control/MotorControl/Src/FOC_SPMSM_Sensorless.su ./AC_Motor_Control/MotorControl/Src/MotorControl.cyclo ./AC_Motor_Control/MotorControl/Src/MotorControl.d ./AC_Motor_Control/MotorControl/Src/MotorControl.o ./AC_Motor_Control/MotorControl/Src/MotorControl.su ./AC_Motor_Control/MotorControl/Src/ShrtctOpnTst.cyclo ./AC_Motor_Control/MotorControl/Src/ShrtctOpnTst.d ./AC_Motor_Control/MotorControl/Src/ShrtctOpnTst.o ./AC_Motor_Control/MotorControl/Src/ShrtctOpnTst.su

.PHONY: clean-AC_Motor_Control-2f-MotorControl-2f-Src

