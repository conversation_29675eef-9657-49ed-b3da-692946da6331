################################################################################
# Automatically-generated file. Do not edit!
# Toolchain: GNU Tools for STM32 (13.3.rel1)
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
CPP_SRCS += \
../STM32G4_Drivers/BoardConfigs/BoardConfig.cpp 

OBJS += \
./STM32G4_Drivers/BoardConfigs/BoardConfig.o 

CPP_DEPS += \
./STM32G4_Drivers/BoardConfigs/BoardConfig.d 


# Each subdirectory must supply rules for building sources it contributes
STM32G4_Drivers/BoardConfigs/%.o STM32G4_Drivers/BoardConfigs/%.su STM32G4_Drivers/BoardConfigs/%.cyclo: ../STM32G4_Drivers/BoardConfigs/%.cpp STM32G4_Drivers/BoardConfigs/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32G431xx -DUSE_HAL_DRIVER -DDEBUG -c -I../Core/Inc -I../Drivers/STM32G4xx_HAL_Driver/Inc -I../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I../Drivers/CMSIS/Include -I../AC_Motor_Control -I../STM32G4_Drivers -I../LogicControl/Inc -I../Devices -I../Modules/Inc -Og -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-threadsafe-statics -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"

clean: clean-STM32G4_Drivers-2f-BoardConfigs

clean-STM32G4_Drivers-2f-BoardConfigs:
	-$(RM) ./STM32G4_Drivers/BoardConfigs/BoardConfig.cyclo ./STM32G4_Drivers/BoardConfigs/BoardConfig.d ./STM32G4_Drivers/BoardConfigs/BoardConfig.o ./STM32G4_Drivers/BoardConfigs/BoardConfig.su

.PHONY: clean-STM32G4_Drivers-2f-BoardConfigs

