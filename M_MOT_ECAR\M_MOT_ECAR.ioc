#MicroXplorer Configuration settings - do not modify
ADC1.Channel-0\#ChannelRegularConversion=ADC_CHANNEL_3
ADC1.Channel-1\#ChannelRegularConversion=ADC_CHANNEL_1
ADC1.Channel-2\#ChannelRegularConversion=ADC_CHANNEL_2
ADC1.Channel-4\#ChannelRegularConversion=ADC_CHANNEL_7
ADC1.ClockPrescaler=ADC_CLOCK_SYNC_PCLK_DIV4
ADC1.CommonPathInternal=null|null|null|null
ADC1.DMAContinuousRequests=ENABLE
ADC1.EnableAnalogWatchDog1=false
ADC1.ExternalTrigConv=ADC_EXTERNALTRIG_T1_TRGO2
ADC1.IPParameters=Rank-0\#ChannelRegularConversion,Channel-0\#ChannelRegularConversion,SamplingTime-0\#ChannelRegularConversion,OffsetNumber-0\#ChannelRegularConversion,NbrOfConversionFlag,Mode,Rank-1\#ChannelRegularConversion,Channel-1\#ChannelRegularConversion,SamplingTime-1\#ChannelRegularConversion,OffsetNumber-1\#ChannelRegularConversion,Rank-2\#ChannelRegularConversion,Channel-2\#ChannelRegularConversion,SamplingTime-2\#ChannelRegularConversion,OffsetNumber-2\#ChannelRegularConversion,NbrOfConversion,ExternalTrigConv,DMAContinuousRequests,Overrun,ClockPrescaler,EnableAnalogWatchDog1,Rank-4\#ChannelRegularConversion,Channel-4\#ChannelRegularConversion,SamplingTime-4\#ChannelRegularConversion,OffsetNumber-4\#ChannelRegularConversion,master,CommonPathInternal
ADC1.Mode=ADC_MODE_INDEPENDENT
ADC1.NbrOfConversion=4
ADC1.NbrOfConversionFlag=1
ADC1.OffsetNumber-0\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC1.OffsetNumber-1\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC1.OffsetNumber-2\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC1.OffsetNumber-4\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC1.Overrun=ADC_OVR_DATA_PRESERVED
ADC1.Rank-0\#ChannelRegularConversion=1
ADC1.Rank-1\#ChannelRegularConversion=2
ADC1.Rank-2\#ChannelRegularConversion=3
ADC1.Rank-4\#ChannelRegularConversion=4
ADC1.SamplingTime-0\#ChannelRegularConversion=ADC_SAMPLETIME_2CYCLES_5
ADC1.SamplingTime-1\#ChannelRegularConversion=ADC_SAMPLETIME_2CYCLES_5
ADC1.SamplingTime-2\#ChannelRegularConversion=ADC_SAMPLETIME_2CYCLES_5
ADC1.SamplingTime-4\#ChannelRegularConversion=ADC_SAMPLETIME_2CYCLES_5
ADC1.master=1
ADC2.Channel-0\#ChannelRegularConversion=ADC_CHANNEL_3
ADC2.Channel-1\#ChannelRegularConversion=ADC_CHANNEL_4
ADC2.Channel-2\#ChannelRegularConversion=ADC_CHANNEL_8
ADC2.Channel-3\#ChannelRegularConversion=ADC_CHANNEL_17
ADC2.CommonPathInternal=null|null|null|null
ADC2.DMAContinuousRequests=ENABLE
ADC2.ExternalTrigConv=ADC_EXTERNALTRIG_T1_TRGO2
ADC2.IPParameters=Rank-0\#ChannelRegularConversion,Channel-0\#ChannelRegularConversion,SamplingTime-0\#ChannelRegularConversion,OffsetNumber-0\#ChannelRegularConversion,NbrOfConversionFlag,Rank-1\#ChannelRegularConversion,Channel-1\#ChannelRegularConversion,SamplingTime-1\#ChannelRegularConversion,OffsetNumber-1\#ChannelRegularConversion,Rank-2\#ChannelRegularConversion,Channel-2\#ChannelRegularConversion,SamplingTime-2\#ChannelRegularConversion,OffsetNumber-2\#ChannelRegularConversion,Rank-3\#ChannelRegularConversion,Channel-3\#ChannelRegularConversion,SamplingTime-3\#ChannelRegularConversion,OffsetNumber-3\#ChannelRegularConversion,NbrOfConversion,DMAContinuousRequests,Overrun,ExternalTrigConv,CommonPathInternal
ADC2.NbrOfConversion=4
ADC2.NbrOfConversionFlag=1
ADC2.OffsetNumber-0\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC2.OffsetNumber-1\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC2.OffsetNumber-2\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC2.OffsetNumber-3\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC2.Overrun=ADC_OVR_DATA_PRESERVED
ADC2.Rank-0\#ChannelRegularConversion=1
ADC2.Rank-1\#ChannelRegularConversion=2
ADC2.Rank-2\#ChannelRegularConversion=3
ADC2.Rank-3\#ChannelRegularConversion=4
ADC2.SamplingTime-0\#ChannelRegularConversion=ADC_SAMPLETIME_2CYCLES_5
ADC2.SamplingTime-1\#ChannelRegularConversion=ADC_SAMPLETIME_2CYCLES_5
ADC2.SamplingTime-2\#ChannelRegularConversion=ADC_SAMPLETIME_2CYCLES_5
ADC2.SamplingTime-3\#ChannelRegularConversion=ADC_SAMPLETIME_2CYCLES_5
Dma.ADC1.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC1.0.EventEnable=ENABLE
Dma.ADC1.0.Instance=DMA1_Channel1
Dma.ADC1.0.MemDataAlignment=DMA_MDATAALIGN_HALFWORD
Dma.ADC1.0.MemInc=DMA_MINC_ENABLE
Dma.ADC1.0.Mode=DMA_CIRCULAR
Dma.ADC1.0.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.ADC1.0.PeriphInc=DMA_PINC_DISABLE
Dma.ADC1.0.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.ADC1.0.Priority=DMA_PRIORITY_LOW
Dma.ADC1.0.RequestNumber=1
Dma.ADC1.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.ADC1.0.SignalID=NONE
Dma.ADC1.0.SyncEnable=DISABLE
Dma.ADC1.0.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.ADC1.0.SyncRequestNumber=4
Dma.ADC1.0.SyncSignalID=NONE
Dma.ADC2.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC2.1.EventEnable=ENABLE
Dma.ADC2.1.Instance=DMA1_Channel2
Dma.ADC2.1.MemDataAlignment=DMA_MDATAALIGN_HALFWORD
Dma.ADC2.1.MemInc=DMA_MINC_ENABLE
Dma.ADC2.1.Mode=DMA_CIRCULAR
Dma.ADC2.1.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.ADC2.1.PeriphInc=DMA_PINC_DISABLE
Dma.ADC2.1.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.ADC2.1.Priority=DMA_PRIORITY_LOW
Dma.ADC2.1.RequestNumber=1
Dma.ADC2.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.ADC2.1.SignalID=NONE
Dma.ADC2.1.SyncEnable=DISABLE
Dma.ADC2.1.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.ADC2.1.SyncRequestNumber=4
Dma.ADC2.1.SyncSignalID=NONE
Dma.Request0=ADC1
Dma.Request1=ADC2
Dma.Request2=SPI3_RX
Dma.Request3=SPI3_TX
Dma.RequestsNb=4
Dma.SPI3_RX.2.Direction=DMA_PERIPH_TO_MEMORY
Dma.SPI3_RX.2.EventEnable=ENABLE
Dma.SPI3_RX.2.Instance=DMA1_Channel3
Dma.SPI3_RX.2.MemDataAlignment=DMA_MDATAALIGN_HALFWORD
Dma.SPI3_RX.2.MemInc=DMA_MINC_ENABLE
Dma.SPI3_RX.2.Mode=DMA_NORMAL
Dma.SPI3_RX.2.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.SPI3_RX.2.PeriphInc=DMA_PINC_DISABLE
Dma.SPI3_RX.2.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.SPI3_RX.2.Priority=DMA_PRIORITY_LOW
Dma.SPI3_RX.2.RequestNumber=1
Dma.SPI3_RX.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.SPI3_RX.2.SignalID=NONE
Dma.SPI3_RX.2.SyncEnable=DISABLE
Dma.SPI3_RX.2.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.SPI3_RX.2.SyncRequestNumber=1
Dma.SPI3_RX.2.SyncSignalID=NONE
Dma.SPI3_TX.3.Direction=DMA_MEMORY_TO_PERIPH
Dma.SPI3_TX.3.EventEnable=ENABLE
Dma.SPI3_TX.3.Instance=DMA1_Channel4
Dma.SPI3_TX.3.MemDataAlignment=DMA_MDATAALIGN_HALFWORD
Dma.SPI3_TX.3.MemInc=DMA_MINC_ENABLE
Dma.SPI3_TX.3.Mode=DMA_NORMAL
Dma.SPI3_TX.3.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.SPI3_TX.3.PeriphInc=DMA_PINC_DISABLE
Dma.SPI3_TX.3.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.SPI3_TX.3.Priority=DMA_PRIORITY_LOW
Dma.SPI3_TX.3.RequestNumber=1
Dma.SPI3_TX.3.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.SPI3_TX.3.SignalID=NONE
Dma.SPI3_TX.3.SyncEnable=DISABLE
Dma.SPI3_TX.3.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.SPI3_TX.3.SyncRequestNumber=1
Dma.SPI3_TX.3.SyncSignalID=NONE
FDCAN1.AutoRetransmission=ENABLE
FDCAN1.CalculateBaudRateNominal=500000
FDCAN1.CalculateTimeBitNominal=2000
FDCAN1.CalculateTimeQuantumNominal=23.80952380952381
FDCAN1.ClockDivider=FDCAN_CLOCK_DIV4
FDCAN1.DataSyncJumpWidth=4
FDCAN1.DataTimeSeg1=5
FDCAN1.DataTimeSeg2=4
FDCAN1.ExtFiltersNbr=8
FDCAN1.FrameFormat=FDCAN_FRAME_CLASSIC
FDCAN1.IPParameters=ClockDivider,FrameFormat,AutoRetransmission,TransmitPause,NominalSyncJumpWidth,NominalTimeSeg1,NominalTimeSeg2,DataSyncJumpWidth,DataTimeSeg1,DataTimeSeg2,StdFiltersNbr,ExtFiltersNbr,NominalPrescaler,CalculateTimeQuantumNominal,CalculateTimeBitNominal,CalculateBaudRateNominal
FDCAN1.NominalPrescaler=1
FDCAN1.NominalSyncJumpWidth=16
FDCAN1.NominalTimeSeg1=65
FDCAN1.NominalTimeSeg2=18
FDCAN1.StdFiltersNbr=28
FDCAN1.TransmitPause=ENABLE
File.Version=6
GPIO.groupedBy=Group By Peripherals
I2C3.I2C_Speed_Mode=I2C_Fast
I2C3.IPParameters=Timing,I2C_Speed_Mode
I2C3.Timing=0x20501E65
KeepUserPlacement=false
Mcu.Family=STM32G4
Mcu.IP0=ADC1
Mcu.IP1=ADC2
Mcu.IP10=TIM3
Mcu.IP11=TIM6
Mcu.IP12=USART3
Mcu.IP2=DMA
Mcu.IP3=FDCAN1
Mcu.IP4=I2C3
Mcu.IP5=NVIC
Mcu.IP6=RCC
Mcu.IP7=SPI3
Mcu.IP8=SYS
Mcu.IP9=TIM1
Mcu.IPNb=13
Mcu.Name=STM32G431R(6-8-B)Tx
Mcu.Package=LQFP64
Mcu.Pin0=PC13
Mcu.Pin1=PC14-OSC32_IN
Mcu.Pin10=PA2
Mcu.Pin11=PA3
Mcu.Pin12=PA4
Mcu.Pin13=PA5
Mcu.Pin14=PA6
Mcu.Pin15=PA7
Mcu.Pin16=PC4
Mcu.Pin17=PC5
Mcu.Pin18=PB0
Mcu.Pin19=PB1
Mcu.Pin2=PC15-OSC32_OUT
Mcu.Pin20=PB2
Mcu.Pin21=PB10
Mcu.Pin22=PB11
Mcu.Pin23=PB12
Mcu.Pin24=PB13
Mcu.Pin25=PB14
Mcu.Pin26=PB15
Mcu.Pin27=PC6
Mcu.Pin28=PC7
Mcu.Pin29=PC8
Mcu.Pin3=PF0-OSC_IN
Mcu.Pin30=PC9
Mcu.Pin31=PA8
Mcu.Pin32=PA9
Mcu.Pin33=PA10
Mcu.Pin34=PA11
Mcu.Pin35=PA12
Mcu.Pin36=PA13
Mcu.Pin37=PA14
Mcu.Pin38=PA15
Mcu.Pin39=PC10
Mcu.Pin4=PF1-OSC_OUT
Mcu.Pin40=PC11
Mcu.Pin41=PC12
Mcu.Pin42=PD2
Mcu.Pin43=PB3
Mcu.Pin44=PB5
Mcu.Pin45=PB6
Mcu.Pin46=PB7
Mcu.Pin47=PB9
Mcu.Pin48=VP_SYS_VS_Systick
Mcu.Pin49=VP_SYS_VS_DBSignals
Mcu.Pin5=PC1
Mcu.Pin50=VP_TIM1_VS_ClockSourceINT
Mcu.Pin51=VP_TIM1_VS_no_output5
Mcu.Pin52=VP_TIM6_VS_ClockSourceINT
Mcu.Pin6=PC2
Mcu.Pin7=PC3
Mcu.Pin8=PA0
Mcu.Pin9=PA1
Mcu.PinsNb=53
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32G431RBTx
MxCube.Version=6.4.0
MxDb.Version=DB.6.0.40
NVIC.ADC1_2_IRQn=true\:0\:0\:false\:false\:false\:true\:false
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.ForceEnableDMAVector=false
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:true\:false\:false\:false\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false
PA0.GPIOParameters=GPIO_Label
PA0.GPIO_Label=KSI_VOLT
PA0.Locked=true
PA0.Mode=IN1-Single-Ended
PA0.Signal=ADC1_IN1
PA1.GPIOParameters=GPIO_Label
PA1.GPIO_Label=MOT_TEMP
PA1.Locked=true
PA1.Mode=IN2-Single-Ended
PA1.Signal=ADC1_IN2
PA10.GPIOParameters=GPIO_Label
PA10.GPIO_Label=WH_MCU
PA10.Locked=true
PA10.Signal=S_TIM1_CH3
PA11.GPIOParameters=GPIO_Label
PA11.GPIO_Label=CAN0_RX
PA11.Locked=true
PA11.Mode=FDCAN_Activate
PA11.Signal=FDCAN1_RX
PA12.GPIOParameters=GPIO_Label
PA12.GPIO_Label=CAN0_TX
PA12.Mode=FDCAN_Activate
PA12.Signal=FDCAN1_TX
PA13.GPIOParameters=GPIO_Label
PA13.GPIO_Label=SWD_DIO
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.GPIOParameters=GPIO_Label
PA14.GPIO_Label=SWD_CLK
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA15.GPIOParameters=GPIO_Label
PA15.GPIO_Label=OUTZ
PA15.Locked=true
PA15.Signal=S_TIM2_ETR
PA2.GPIOParameters=GPIO_Label
PA2.GPIO_Label=IU
PA2.Locked=true
PA2.Mode=IN3-Single-Ended
PA2.Signal=ADC1_IN3
PA3.GPIOParameters=GPIO_Label
PA3.GPIO_Label=Switch_DI15
PA3.Locked=true
PA3.Signal=GPIO_Input
PA4.GPIOParameters=GPIO_Label
PA4.GPIO_Label=ConTemp
PA4.Locked=true
PA4.Mode=IN17-Single-Ended
PA4.Signal=ADC2_IN17
PA5.GPIOParameters=GPIO_Label
PA5.GPIO_Label=OUTA
PA5.Locked=true
PA5.Signal=S_TIM2_CH1
PA6.GPIOParameters=GPIO_Label
PA6.GPIO_Label=IV
PA6.Locked=true
PA6.Mode=IN3-Single-Ended
PA6.Signal=ADC2_IN3
PA7.GPIOParameters=GPIO_Label
PA7.GPIO_Label=LOGIC_VOLT
PA7.Locked=true
PA7.Mode=IN4-Single-Ended
PA7.Signal=ADC2_IN4
PA8.GPIOParameters=GPIO_Label
PA8.GPIO_Label=UH_MCU
PA8.Locked=true
PA8.Signal=S_TIM1_CH1
PA9.GPIOParameters=GPIO_Label
PA9.GPIO_Label=VH_MCU
PA9.Locked=true
PA9.Signal=S_TIM1_CH2
PB0.GPIOParameters=GPIO_Label
PB0.GPIO_Label=PWM_NMC
PB0.Locked=true
PB0.Signal=S_TIM3_CH3
PB1.GPIOParameters=GPIO_Label
PB1.GPIO_Label=DI1-MCU
PB1.Locked=true
PB1.Signal=GPIO_Input
PB10.Locked=true
PB10.Mode=Asynchronous
PB10.Signal=USART3_TX
PB11.Mode=Asynchronous
PB11.Signal=USART3_RX
PB12.Locked=true
PB12.Mode=Activate-Break-Input
PB12.Signal=TIM1_BKIN
PB13.GPIOParameters=GPIO_Label
PB13.GPIO_Label=UL_MCU
PB13.Locked=true
PB13.Mode=PWM Generation1 CH1 CH1N
PB13.Signal=TIM1_CH1N
PB14.GPIOParameters=GPIO_Label
PB14.GPIO_Label=VL_MCU
PB14.Locked=true
PB14.Mode=PWM Generation2 CH2 CH2N
PB14.Signal=TIM1_CH2N
PB15.GPIOParameters=GPIO_Label
PB15.GPIO_Label=WL_MCH
PB15.Locked=true
PB15.Mode=PWM Generation3 CH3 CH3N
PB15.Signal=TIM1_CH3N
PB2.GPIOParameters=GPIO_Label
PB2.GPIO_Label=Curise_Switch
PB2.Locked=true
PB2.Signal=GPIO_Input
PB3.GPIOParameters=GPIO_Label
PB3.GPIO_Label=OUTB
PB3.Locked=true
PB3.Signal=S_TIM2_CH2
PB5.GPIOParameters=GPIO_Label
PB5.GPIO_Label=RDC_SAMPLE
PB5.Locked=true
PB5.Signal=GPIO_Output
PB6.GPIOParameters=GPIO_Label
PB6.GPIO_Label=S_0
PB6.Locked=true
PB6.Signal=GPIO_Input
PB7.GPIOParameters=PinState,GPIO_Label
PB7.GPIO_Label=E+5V-CTRL
PB7.Locked=true
PB7.PinState=GPIO_PIN_SET
PB7.Signal=GPIO_Output
PB9.GPIOParameters=GPIO_Label
PB9.GPIO_Label=S_1
PB9.Locked=true
PB9.Signal=GPIO_Input
PC1.GPIOParameters=GPIO_Label
PC1.GPIO_Label=Throttle_Volt
PC1.Locked=true
PC1.Mode=IN7-Single-Ended
PC1.Signal=ADC1_IN7
PC10.GPIOParameters=GPIO_Label
PC10.GPIO_Label=RDC_SCK
PC10.Locked=true
PC10.Mode=Full_Duplex_Master
PC10.Signal=SPI3_SCK
PC11.GPIOParameters=GPIO_Label
PC11.GPIO_Label=RDC_DATA
PC11.Locked=true
PC11.Mode=Full_Duplex_Master
PC11.Signal=SPI3_MISO
PC12.GPIOParameters=GPIO_Label
PC12.GPIO_Label=RDC_RD
PC12.Locked=true
PC12.Mode=Full_Duplex_Master
PC12.Signal=SPI3_MOSI
PC13.GPIOParameters=GPIO_Label
PC13.GPIO_Label=LED1
PC13.Locked=true
PC13.Signal=GPIO_Output
PC14-OSC32_IN.GPIOParameters=GPIO_Label
PC14-OSC32_IN.GPIO_Label=DI3_MCU
PC14-OSC32_IN.Locked=true
PC14-OSC32_IN.Signal=GPIO_Input
PC15-OSC32_OUT.GPIOParameters=GPIO_Label
PC15-OSC32_OUT.GPIO_Label=DI2-MCU
PC15-OSC32_OUT.Locked=true
PC15-OSC32_OUT.Signal=GPIO_Input
PC2.GPIOParameters=GPIO_Label
PC2.GPIO_Label=BUS_Volt
PC2.Locked=true
PC2.Mode=IN8-Single-Ended
PC2.Signal=ADC2_IN8
PC3.GPIOParameters=GPIO_Label
PC3.GPIO_Label=Switch_DI11
PC3.Locked=true
PC3.Signal=GPIO_Input
PC4.GPIOParameters=GPIO_Label
PC4.GPIO_Label=Status_NMC
PC4.Locked=true
PC4.Signal=GPIO_Input
PC5.GPIOParameters=GPIO_Label
PC5.GPIO_Label=RDC-RST
PC5.Locked=true
PC5.Signal=GPIO_Output
PC6.GPIOParameters=GPIO_Label
PC6.GPIO_Label=RDC_CS
PC6.Locked=true
PC6.Signal=GPIO_Output
PC7.GPIOParameters=GPIO_Label
PC7.GPIO_Label=Wake_Up
PC7.Locked=true
PC7.Signal=GPIO_Output
PC8.GPIOParameters=GPIO_Label,GPIO_Pu
PC8.GPIO_Label=I2C3_SCL
PC8.GPIO_Pu=GPIO_PULLUP
PC8.Locked=true
PC8.Mode=I2C
PC8.Signal=I2C3_SCL
PC9.Locked=true
PC9.Mode=I2C
PC9.Signal=I2C3_SDA
PD2.GPIOParameters=GPIO_Label
PD2.GPIO_Label=S-2
PD2.Locked=true
PD2.Signal=GPIO_Input
PF0-OSC_IN.Locked=true
PF0-OSC_IN.Mode=HSE-External-Oscillator
PF0-OSC_IN.Signal=RCC_OSC_IN
PF1-OSC_OUT.Locked=true
PF1-OSC_OUT.Mode=HSE-External-Oscillator
PF1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32G431RBTx
ProjectManager.FirmwarePackage=STM32Cube FW_G4 V1.5.1
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=false
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=STM32CubeIDE
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=M_MOT_ECAR.ioc
ProjectManager.ProjectName=M_MOT_ECAR
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=STM32CubeIDE
ProjectManager.ToolChainLocation=
ProjectManager.UnderRoot=true
ProjectManager.functionlistsort=1-MX_GPIO_Init-GPIO-false-HAL-true,2-MX_DMA_Init-DMA-false-HAL-true,3-SystemClock_Config-RCC-false-HAL-false,4-MX_TIM1_Init-TIM1-false-HAL-true,5-MX_ADC1_Init-ADC1-false-HAL-true,6-MX_ADC2_Init-ADC2-false-HAL-true,7-MX_FDCAN1_Init-FDCAN1-false-HAL-true,8-MX_TIM6_Init-TIM6-false-HAL-true,9-MX_USART3_UART_Init-USART3-false-HAL-true,10-MX_SPI3_Init-SPI3-false-HAL-true,11-MX_I2C3_Init-I2C3-false-HAL-true,12-MX_TIM3_Init-TIM3-false-HAL-true
RCC.ADC12Freq_Value=168000000
RCC.AHBFreq_Value=168000000
RCC.APB1Freq_Value=168000000
RCC.APB1TimFreq_Value=168000000
RCC.APB2Freq_Value=168000000
RCC.APB2TimFreq_Value=168000000
RCC.CRSFreq_Value=48000000
RCC.CortexFreq_Value=168000000
RCC.EXTERNAL_CLOCK_VALUE=12288000
RCC.FCLKCortexFreq_Value=168000000
RCC.FDCANFreq_Value=168000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=168000000
RCC.HSE_VALUE=8000000
RCC.HSI48_VALUE=48000000
RCC.HSI_VALUE=16000000
RCC.I2C1Freq_Value=168000000
RCC.I2C2Freq_Value=168000000
RCC.I2C3Freq_Value=168000000
RCC.I2SFreq_Value=168000000
RCC.IPParameters=ADC12Freq_Value,AHBFreq_Value,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,CRSFreq_Value,CortexFreq_Value,EXTERNAL_CLOCK_VALUE,FCLKCortexFreq_Value,FDCANFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI48_VALUE,HSI_VALUE,I2C1Freq_Value,I2C2Freq_Value,I2C3Freq_Value,I2SFreq_Value,LPTIM1Freq_Value,LPUART1Freq_Value,LSCOPinFreq_Value,LSE_VALUE,LSI_VALUE,MCO1PinFreq_Value,PLLN,PLLPoutputFreq_Value,PLLQoutputFreq_Value,PLLRCLKFreq_Value,PLLSourceVirtual,PWRFreq_Value,RNGFreq_Value,SAI1Freq_Value,SYSCLKFreq_VALUE,SYSCLKSource,UART4Freq_Value,USART1Freq_Value,USART2Freq_Value,USART3Freq_Value,USBFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value
RCC.LPTIM1Freq_Value=168000000
RCC.LPUART1Freq_Value=168000000
RCC.LSCOPinFreq_Value=32000
RCC.LSE_VALUE=32768
RCC.LSI_VALUE=32000
RCC.MCO1PinFreq_Value=16000000
RCC.PLLN=42
RCC.PLLPoutputFreq_Value=168000000
RCC.PLLQoutputFreq_Value=168000000
RCC.PLLRCLKFreq_Value=168000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.PWRFreq_Value=168000000
RCC.RNGFreq_Value=168000000
RCC.SAI1Freq_Value=168000000
RCC.SYSCLKFreq_VALUE=168000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.UART4Freq_Value=168000000
RCC.USART1Freq_Value=168000000
RCC.USART2Freq_Value=168000000
RCC.USART3Freq_Value=168000000
RCC.USBFreq_Value=168000000
RCC.VCOInputFreq_Value=8000000
RCC.VCOOutputFreq_Value=336000000
SH.S_TIM1_CH1.0=TIM1_CH1,PWM Generation1 CH1 CH1N
SH.S_TIM1_CH1.ConfNb=1
SH.S_TIM1_CH2.0=TIM1_CH2,PWM Generation2 CH2 CH2N
SH.S_TIM1_CH2.ConfNb=1
SH.S_TIM1_CH3.0=TIM1_CH3,PWM Generation3 CH3 CH3N
SH.S_TIM1_CH3.ConfNb=1
SH.S_TIM2_CH1.0=TIM2_CH1
SH.S_TIM2_CH1.ConfNb=1
SH.S_TIM2_CH2.0=TIM2_CH2
SH.S_TIM2_CH2.ConfNb=1
SH.S_TIM2_ETR.0=TIM2_ETR
SH.S_TIM2_ETR.ConfNb=1
SH.S_TIM3_CH3.0=TIM3_CH3,PWM Generation3 CH3
SH.S_TIM3_CH3.ConfNb=1
SPI3.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_32
SPI3.CLKPolarity=SPI_POLARITY_HIGH
SPI3.CalculateBaudRate=5.25 MBits/s
SPI3.DataSize=SPI_DATASIZE_16BIT
SPI3.Direction=SPI_DIRECTION_2LINES
SPI3.IPParameters=VirtualType,Mode,Direction,BaudRatePrescaler,CalculateBaudRate,DataSize,CLKPolarity
SPI3.Mode=SPI_MODE_MASTER
SPI3.VirtualType=VM_MASTER
TIM1.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM1.Break2Polarity=TIM_BREAK2POLARITY_LOW
TIM1.BreakPolarity=TIM_BREAKPOLARITY_HIGH
TIM1.Channel-PWM\ Generation1\ CH1\ CH1N=TIM_CHANNEL_1
TIM1.Channel-PWM\ Generation2\ CH2\ CH2N=TIM_CHANNEL_2
TIM1.Channel-PWM\ Generation3\ CH3\ CH3N=TIM_CHANNEL_3
TIM1.Channel-PWM\ Generation5\ No\ Output=TIM_CHANNEL_5
TIM1.CounterMode=TIM_COUNTERMODE_CENTERALIGNED1
TIM1.Dithering=Disable
TIM1.IPParameters=Channel-PWM Generation5 No Output,CounterMode,Dithering,TIM_MasterOutputTrigger2,OCMode_PWM-PWM Generation5 No Output,Channel-PWM Generation1 CH1 CH1N,Channel-PWM Generation2 CH2 CH2N,Channel-PWM Generation3 CH3 CH3N,OC1Preload_PWM,OC2Preload_PWM,OC3Preload_PWM,AutoReloadPreload,OC5Preload_PWM,BreakPolarity,SourceBRKDigInputPolarity,Break2Polarity
TIM1.OC1Preload_PWM=DISABLE
TIM1.OC2Preload_PWM=DISABLE
TIM1.OC3Preload_PWM=DISABLE
TIM1.OC5Preload_PWM=DISABLE
TIM1.OCMode_PWM-PWM\ Generation5\ No\ Output=TIM_OCMODE_PWM2
TIM1.SourceBRKDigInputPolarity=TIM_BREAKINPUTSOURCE_POLARITY_LOW
TIM1.TIM_MasterOutputTrigger2=TIM_TRGO2_OC5REF_RISING_OC6REF_RISING
TIM3.Channel-PWM\ Generation3\ CH3=TIM_CHANNEL_3
TIM3.IPParameters=Channel-PWM Generation3 CH3
TIM6.IPParameters=Prescaler
TIM6.IPParametersWithoutCheck=Prescaler
TIM6.Prescaler=HAL_RCC_GetSysClockFreq() / 1000000 - 1
USART3.DMADisableonRxErrorParam=ADVFEATURE_DMA_DISABLEONRXERROR
USART3.IPParameters=VirtualMode-Asynchronous,DMADisableonRxErrorParam
USART3.VirtualMode-Asynchronous=VM_ASYNC
VP_SYS_VS_DBSignals.Mode=DisableDeadBatterySignals
VP_SYS_VS_DBSignals.Signal=SYS_VS_DBSignals
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM1_VS_ClockSourceINT.Mode=Internal
VP_TIM1_VS_ClockSourceINT.Signal=TIM1_VS_ClockSourceINT
VP_TIM1_VS_no_output5.Mode=PWM Generation5 No Output
VP_TIM1_VS_no_output5.Signal=TIM1_VS_no_output5
VP_TIM6_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM6_VS_ClockSourceINT.Signal=TIM6_VS_ClockSourceINT
board=custom
isbadioc=false
