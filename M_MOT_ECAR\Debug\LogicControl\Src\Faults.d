LogicControl/Src/Faults.o: ../LogicControl/Src/Faults.c \
 C:/Users/<USER>/Desktop/zz/EC12/M_MOT_Standard_DREAME_EC12/EC12/Core/Inc/include.h \
 c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\typedefine.h \
 ../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal.h \
 ../Core/Inc/stm32g4xx_hal_conf.h \
 ../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc.h \
 ../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_def.h \
 ../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h \
 ../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g431xx.h \
 ../Drivers/CMSIS/Include/core_cm4.h \
 ../Drivers/CMSIS/Include/cmsis_version.h \
 ../Drivers/CMSIS/Include/cmsis_compiler.h \
 ../Drivers/CMSIS/Include/cmsis_gcc.h \
 ../Drivers/CMSIS/Include/mpu_armv7.h \
 ../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h \
 ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h \
 ../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc_ex.h \
 ../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio.h \
 ../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio_ex.h \
 ../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma.h \
 ../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma_ex.h \
 ../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_cortex.h \
 ../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_adc.h \
 ../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_ll_adc.h \
 ../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_adc_ex.h \
 ../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_exti.h \
 ../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_fdcan.h \
 ../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash.h \
 ../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ex.h \
 ../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ramfunc.h \
 ../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_i2c.h \
 ../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_i2c_ex.h \
 ../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr.h \
 ../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr_ex.h \
 ../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_tim.h \
 ../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_tim_ex.h \
 ../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart.h \
 ../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart_ex.h \
 c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\PICal.h \
 c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\vardeclare.h \
 c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\typedefine.h \
 c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\msgprocess.h \
 c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\can.h \
 c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\store.h \
 c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\can.h \
 c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\pical.h \
 C:/Users/<USER>/Desktop/zz/EC12/M_MOT_Standard_DREAME_EC12/EC12/Core/Inc/fdcan.h \
 C:/Users/<USER>/Desktop/zz/EC12/M_MOT_Standard_DREAME_EC12/EC12/Core/Inc/main.h \
 c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\systemconfig.h \
 c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\MotParm_Ec01.h \
 c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\candbgdata.h \
 c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\faults.h \
 c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\vcudemand.h \
 c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\mainloop.h \
 ../AC_Motor_Control/MotorControl\Inc\MotorControl.h \
 ../STM32G4_Drivers/Inc\Drivers.h ../STM32G4_Drivers/Inc\Common.h \
 ../STM32G4_Drivers/Inc\ADC.h ../Core/Inc/GlobalConfig.h \
 ../STM32G4_Drivers/BoardConfigs/EC90.h \
 ../STM32G4_Drivers/BoardConfigs/../../LogicControl/Inc/Typedefine.h \
 ../AC_Motor_Control/MotorParams/QL_5kW_250A_V1.h \
 ../AC_Motor_Control/MotorParams/..\MotorControl\Inc\FOC_PM_V1.h \
 ../AC_Motor_Control/MotorParams/..\MotorControl\Inc\FOC_Base.h \
 ../AC_Motor_Control/MotorParams/..\MotorControl\Inc\../../Common/Inc/UserMaths.h \
 ../STM32G4_Drivers/Inc\Timer.h \
 ../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g431xx.h \
 ../AC_Motor_Control/Common\Inc\GlobalSymbols.h \
 ../AC_Motor_Control/Common\Inc\UserMaths.h \
 ../STM32G4_Drivers/Inc\PGA411.h ../STM32G4_Drivers/Inc\Timer.h \
 ../AC_Motor_Control/Common/Inc/GlobalSymbols.h \
 ../STM32G4_Drivers/Inc\MagEncoder.h ../STM32G4_Drivers/Inc\QEP.h \
 ../STM32G4_Drivers/Inc\PWM.h ../STM32G4_Drivers/Inc\SCI.h \
 ../STM32G4_Drivers/Inc\SpeedCap.h ../STM32G4_Drivers/Inc\Hall.h \
 ../AC_Motor_Control/MotorControl\Inc\..\..\Common\Inc\GlobalSymbols.h \
 ../AC_Motor_Control/MotorControl\Inc\..\..\Common\Inc\UserMaths.h \
 ../STM32G4_Drivers/Inc\Common.h \
 ../AC_Motor_Control/MotorControl\Inc\..\..\Common\Inc\PstnSnsr.h \
 ../AC_Motor_Control/MotorControl\Inc\..\..\Common\Inc\GlobalSymbols.h \
 ../AC_Motor_Control/MotorControl\Inc\..\..\Common\Inc\..\..\MotorControl\Inc\FOC_Base.h \
 c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\usartprocess.h \
 ../Core/Inc/usart.h ../Core/Inc/main.h \
 ../AC_Motor_Control/../LogicControl/Inc/Ec90_Protocol.h

C:/Users/<USER>/Desktop/zz/EC12/M_MOT_Standard_DREAME_EC12/EC12/Core/Inc/include.h:

c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\typedefine.h:

../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal.h:

../Core/Inc/stm32g4xx_hal_conf.h:

../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc.h:

../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_def.h:

../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g4xx.h:

../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g431xx.h:

../Drivers/CMSIS/Include/core_cm4.h:

../Drivers/CMSIS/Include/cmsis_version.h:

../Drivers/CMSIS/Include/cmsis_compiler.h:

../Drivers/CMSIS/Include/cmsis_gcc.h:

../Drivers/CMSIS/Include/mpu_armv7.h:

../Drivers/CMSIS/Device/ST/STM32G4xx/Include/system_stm32g4xx.h:

../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h:

../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_rcc_ex.h:

../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio.h:

../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_gpio_ex.h:

../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma.h:

../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_dma_ex.h:

../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_cortex.h:

../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_adc.h:

../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_ll_adc.h:

../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_adc_ex.h:

../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_exti.h:

../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_fdcan.h:

../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash.h:

../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ex.h:

../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_flash_ramfunc.h:

../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_i2c.h:

../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_i2c_ex.h:

../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr.h:

../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_pwr_ex.h:

../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_tim.h:

../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_tim_ex.h:

../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart.h:

../Drivers/STM32G4xx_HAL_Driver/Inc/stm32g4xx_hal_uart_ex.h:

c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\PICal.h:

c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\vardeclare.h:

c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\typedefine.h:

c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\msgprocess.h:

c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\can.h:

c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\store.h:

c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\can.h:

c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\pical.h:

C:/Users/<USER>/Desktop/zz/EC12/M_MOT_Standard_DREAME_EC12/EC12/Core/Inc/fdcan.h:

C:/Users/<USER>/Desktop/zz/EC12/M_MOT_Standard_DREAME_EC12/EC12/Core/Inc/main.h:

c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\systemconfig.h:

c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\MotParm_Ec01.h:

c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\candbgdata.h:

c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\faults.h:

c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\vcudemand.h:

c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\mainloop.h:

../AC_Motor_Control/MotorControl\Inc\MotorControl.h:

../STM32G4_Drivers/Inc\Drivers.h:

../STM32G4_Drivers/Inc\Common.h:

../STM32G4_Drivers/Inc\ADC.h:

../Core/Inc/GlobalConfig.h:

../STM32G4_Drivers/BoardConfigs/EC90.h:

../STM32G4_Drivers/BoardConfigs/../../LogicControl/Inc/Typedefine.h:

../AC_Motor_Control/MotorParams/QL_5kW_250A_V1.h:

../AC_Motor_Control/MotorParams/..\MotorControl\Inc\FOC_PM_V1.h:

../AC_Motor_Control/MotorParams/..\MotorControl\Inc\FOC_Base.h:

../AC_Motor_Control/MotorParams/..\MotorControl\Inc\../../Common/Inc/UserMaths.h:

../STM32G4_Drivers/Inc\Timer.h:

../Drivers/CMSIS/Device/ST/STM32G4xx/Include/stm32g431xx.h:

../AC_Motor_Control/Common\Inc\GlobalSymbols.h:

../AC_Motor_Control/Common\Inc\UserMaths.h:

../STM32G4_Drivers/Inc\PGA411.h:

../STM32G4_Drivers/Inc\Timer.h:

../AC_Motor_Control/Common/Inc/GlobalSymbols.h:

../STM32G4_Drivers/Inc\MagEncoder.h:

../STM32G4_Drivers/Inc\QEP.h:

../STM32G4_Drivers/Inc\PWM.h:

../STM32G4_Drivers/Inc\SCI.h:

../STM32G4_Drivers/Inc\SpeedCap.h:

../STM32G4_Drivers/Inc\Hall.h:

../AC_Motor_Control/MotorControl\Inc\..\..\Common\Inc\GlobalSymbols.h:

../AC_Motor_Control/MotorControl\Inc\..\..\Common\Inc\UserMaths.h:

../STM32G4_Drivers/Inc\Common.h:

../AC_Motor_Control/MotorControl\Inc\..\..\Common\Inc\PstnSnsr.h:

../AC_Motor_Control/MotorControl\Inc\..\..\Common\Inc\GlobalSymbols.h:

../AC_Motor_Control/MotorControl\Inc\..\..\Common\Inc\..\..\MotorControl\Inc\FOC_Base.h:

c:\users\<USER>\desktop\zz\ec12\m_mot_standard_dreame_ec12\ec12\logiccontrol\inc\usartprocess.h:

../Core/Inc/usart.h:

../Core/Inc/main.h:

../AC_Motor_Control/../LogicControl/Inc/Ec90_Protocol.h:
