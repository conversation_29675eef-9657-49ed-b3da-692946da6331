../STM32G4_Drivers/Src/SPI.cpp:552:1:void __static_initialization_and_destruction_0()	3
../STM32G4_Drivers/Src/SPI.cpp:95:6:static void CSPI::PrphInit(const SPI_Prms_t*)	6
../STM32G4_Drivers/Src/SPI.cpp:23:6:void CSPI::SelectPrph(uint8_t)	13
../STM32G4_Drivers/Src/SPI.cpp:130:6:void CSPI::EnablePrph()	1
../STM32G4_Drivers/Src/SPI.cpp:136:6:void CSPI::DisablePrph()	1
../STM32G4_Drivers/Src/../Inc/SPI.h:42:7:CSPI::CSPI()	1
../STM32G4_Drivers/Src/SPI.cpp:142:1:CSPICh::CSPICh()	1
../STM32G4_Drivers/Src/SPI.cpp:149:1:CSPICh::CSPICh(uint8_t)	1
../STM32G4_Drivers/Src/SPI.cpp:156:6:void CSPICh::SetCSPin(GPIOPin_t)	1
../STM32G4_Drivers/Src/SPI.cpp:335:1:CSPISlv::CSPISlv()	1
../STM32G4_Drivers/Src/SPI.cpp:344:1:CSPISlv::CSPISlv(uint8_t)	1
../STM32G4_Drivers/Src/SPI.cpp:391:6:void CSPISlv::CSFPrcs()	3
../STM32G4_Drivers/Src/SPI.cpp:414:6:void CSPISlv::CSRPrcs()	6
../STM32G4_Drivers/Src/SPI.cpp:375:6:void CSPISlv::CSISR()	3
../STM32G4_Drivers/Src/SPI.cpp:354:6:static void CSPISlv::CS1ISR()	1
../STM32G4_Drivers/Src/SPI.cpp:359:6:static void CSPISlv::CS2ISR()	1
../STM32G4_Drivers/Src/SPI.cpp:364:6:static void CSPISlv::CS3ISR()	1
../STM32G4_Drivers/Src/SPI.cpp:369:6:static void CSPISlv::CS4ISR()	1
../STM32G4_Drivers/Src/SPI.cpp:457:6:void CSPISlv::SetCSPin(GPIOPin_t)	1
../STM32G4_Drivers/Src/SPI.cpp:469:6:void CSPISlv::EnableCSISR()	6
../STM32G4_Drivers/Src/SPI.cpp:505:9:uint8_t CSPISlv::GetCSStt()	1
../STM32G4_Drivers/Src/SPI.cpp:515:9:uint8_t CSPISlv::Process()	7
../STM32G4_Drivers/Src/../Inc/Timer.h:58:6:void CTimer<CntType>::SetStart() [with CntType = short unsigned int]	1
../STM32G4_Drivers/Src/../Inc/Timer.h:89:9:uint8_t CTimer<CntType>::CheckTimeElps(CntType) [with CntType = short unsigned int]	2
../STM32G4_Drivers/Src/SPI.cpp:166:9:uint8_t CSPICh::TrnsmtData(uint16_t)	31
../STM32G4_Drivers/Src/SPI.cpp:552:1:cpp)	1
