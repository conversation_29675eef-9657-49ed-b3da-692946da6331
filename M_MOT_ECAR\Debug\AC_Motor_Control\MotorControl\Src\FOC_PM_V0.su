../AC_Motor_Control/MotorControl/Src/FOC_PM_V0.cpp:54:1:CFOC_PM_V0::CFOC_PM_V0()	16	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V0.cpp:97:6:void CFOC_PM_V0::MtrPrmsInit()	4	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V0.cpp:87:6:void CFOC_PM_V0::LoadMtrPrms(const FOCPMV0MtrPrms_t*)	8	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V0.cpp:181:7:float CFOC_PM_V0::MTPACalc(float)	0	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V0.cpp:999:6:void setFOCPMV0Prms_t(const FOCPMV0Prms_t*)	4	static
../STM32G4_Drivers/Inc\Timer.h:58:6:void CTimer<CntType>::SetStart() [with CntType = long unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:89:9:uint8_t CTimer<CntType>::CheckTimeElps(CntType) [with CntType = long unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:71:9:CntType CTimer<CntType>::GetTimeElapsed(uint8_t) [with CntType = long unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:58:6:void CTimer<CntType>::SetStart() [with CntType = short unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:71:9:CntType CTimer<CntType>::GetTimeElapsed(uint8_t) [with CntType = short unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:106:6:void CTimer<CntType>::SaveSmpToRec() [with CntType = short unsigned int]	0	static
../AC_Motor_Control/MotorControl/Src/..\Inc\../../Common/Inc/UserMaths.h:145:7:float CDifferential<InType>::GetVal() [with InType = float]	24	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V0.cpp:190:6:void CFOC_PM_V0::NorPrcs()	24	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V0.cpp:730:6:void CFOC_PM_V0::IsTqMxTest()	8	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V0.cpp:746:6:void CFOC_PM_V0::TqLnTest()	8	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V0.cpp:338:6:void CFOC_PM_V0::IdIqDrctCtl(float, float)	16	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V0.cpp:429:6:void CFOC_PM_V0::PssvEcdrOfst()	24	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V0.cpp:478:6:void CFOC_PM_V0::ActvEcdrOfst()	32	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V0.cpp:622:6:void CFOC_PM_V0::IfMTPATest()	24	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V0.cpp:646:6:void CFOC_PM_V0::EcdrCmpTest()	8	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V0.cpp:776:6:void CFOC_PM_V0::SRMEcdrOfst()	8	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V0.cpp:834:6:void CFOC_PM_V0::ShtDwnPrcs()	8	static
../AC_Motor_Control/MotorControl/Src/FOC_PM_V0.cpp:934:6:void CFOC_PM_V0::ClbPrcs()	16	static
