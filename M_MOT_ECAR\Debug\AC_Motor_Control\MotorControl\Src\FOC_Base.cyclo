../AC_Motor_Control/MotorControl/Src/FOC_Base.cpp:129:6:void CFOC::<PERSON>()	1
../AC_Motor_Control/MotorControl/Src/FOC_Base.cpp:138:6:void CFOC::Park()	1
../AC_Motor_Control/MotorControl/Src/FOC_Base.cpp:150:6:void CFOC::iPark()	1
../AC_Motor_Control/MotorControl/Src/FOC_Base.cpp:163:6:void CFOC::iParkCmpCalc()	1
../AC_Motor_Control/MotorControl/Src/FOC_Base.cpp:177:6:void CFOC::SVPWMCalc()	23
../AC_Motor_Control/MotorControl/Src/FOC_Base.cpp:321:7:float CFOC::VsModToVsFund(float)	2
../AC_Motor_Control/MotorControl/Src/FOC_Base.cpp:336:7:float CFOC::VsFundToVsMod(float)	2
../AC_Motor_Control/MotorControl/Src/FOC_Base.cpp:352:6:void CFOC::VsModRefCalc()	3
../AC_Motor_Control/MotorControl/Src/FOC_Base.cpp:377:6:void CFOC::IBusEstimate()	2
../AC_Motor_Control/MotorControl/Src/FOC_Base.cpp:394:6:void CFOC::CrntLpGnCmp()	3
../AC_Motor_Control/MotorControl/Src/FOC_Base.cpp:423:6:void CFOC::SetModRatio(float)	1
../AC_Motor_Control/MotorControl/Src/FOC_Base.cpp:477:6:void CFOC::GetRtrAngl()	9
../AC_Motor_Control/MotorControl/Src/FOC_Base.cpp:530:6:void CFOC::CrntSnsrClb()	2
../AC_Motor_Control/MotorControl/Src/FOC_Base.cpp:582:6:void setFOCBasePrms_t(const FOCBasePrms_t*)	2
../AC_Motor_Control/MotorControl/Src/..\Inc\../../Common/Inc/UserMaths.h:137:1:CDifferential<InType>::CDifferential() [with InType = float]	1
../AC_Motor_Control/MotorControl/Src/FOC_Base.cpp:68:1:CFOC::CFOC()	1
../STM32G4_Drivers/Inc\Timer.h:58:6:void CTimer<CntType>::SetStart() [with CntType = short unsigned int]	1
../STM32G4_Drivers/Inc\Timer.h:71:9:CntType CTimer<CntType>::GetTimeElapsed(uint8_t) [with CntType = short unsigned int]	2
../STM32G4_Drivers/Inc\Timer.h:106:6:void CTimer<CntType>::SaveSmpToRec() [with CntType = short unsigned int]	1
../AC_Motor_Control/MotorControl/Src/..\Inc\../../Common/Inc/UserMaths.h:145:7:float CDifferential<InType>::GetVal() [with InType = float]	8
../AC_Motor_Control/MotorControl/Src/FOC_Base.cpp:434:6:void CFOC::FixCurVectCtl(float, float)	4
