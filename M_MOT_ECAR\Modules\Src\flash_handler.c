/**
 * @file flash_handler.c
 * @brief Flash操作处理模块实现
 */

#include "flash_handler.h"
#include "uds.h"
#include <stdbool.h>
/**
 * @brief 擦除Flash页
 * @param startAddress 起始地址
 * @param numPages 要擦除的页数
 * @return HAL状态
 */
HAL_StatusTypeDef FLASH_ErasePages(uint32_t startAddress, uint32_t numPages)
{
    HAL_StatusTypeDef status;
    FLASH_EraseInitTypeDef EraseInitStruct;
    uint32_t PageError = 0;

    /* 解锁Flash */
    status = HAL_FLASH_Unlock();
    if (status != HAL_OK)
    {
        return status;
    }

    /* 配置擦除参数 */
    EraseInitStruct.TypeErase = FLASH_TYPEERASE_PAGES;
    EraseInitStruct.Banks = FLASH_BANK_1;                                 // G4系列通常使用BANK1
    EraseInitStruct.Page = (startAddress - FLASH_BASE) / FLASH_PAGE_SIZE; // 计算页号
    EraseInitStruct.NbPages = numPages;

    /* 执行擦除 */
    status = HAL_FLASHEx_Erase(&EraseInitStruct, &PageError);

    /* 锁定Flash */
    HAL_FLASH_Lock();

    return status;
}

/**
 * @brief 将应用程序有效标志写入Flash
 * @param address: 标志位地址
 * @param value: 要写入的值
 * @retval true 如果写入成功, false 如果失败
 */
bool flash_write_app_valid_flag(uint32_t address, uint32_t value)
{
    HAL_StatusTypeDef status = HAL_ERROR;

    // 1. 解锁Flash
    if (HAL_FLASH_Unlock() != HAL_OK)
    {
        isotp_debug("Flash解锁失败!\n");
        return false;
    }

    // 2. 擦除包含标志位的页（写入前必须擦除）
    FLASH_EraseInitTypeDef EraseInitStruct;
    uint32_t PageError = 0;

    // 计算地址所在的页号
    uint32_t page = (address - FLASH_BASE) / FLASH_PAGE_SIZE;

    // 填充擦除结构体
    EraseInitStruct.TypeErase = FLASH_TYPEERASE_PAGES;
    EraseInitStruct.Page = page;
    EraseInitStruct.NbPages = 1;

    status = HAL_FLASHEx_Erase(&EraseInitStruct, &PageError);
    if (status != HAL_OK)
    {
        isotp_debug("擦除App有效标志页失败! 错误码: 0x%lX\n", PageError);
        HAL_FLASH_Lock();
        return false;
    }

    // 3. 写入数据
    // status = HAL_FLASH_Program(FLASH_TYPEPROGRAM_WORD, address, (uint32_t)value);
    // 使用 Double Word 写入，将 32-bit 值放入低位，高位补零
    uint64_t value_64 = (uint64_t)value;
    status = HAL_FLASH_Program(FLASH_TYPEPROGRAM_DOUBLEWORD, address, value_64);

    // 4. 验证写入结果
    bool result = false;
    if (status == HAL_OK)
    {
        // 验证写入的值
        if (*(volatile uint32_t *)address == value)
        {
            isotp_debug("App有效标志写入成功: 0x%lX -> 0x%lX\n", address, value);
            result = true;
        }
        else
        {
            isotp_debug("App有效标志写入验证失败: 期望0x%lX，实际0x%lX\n", value,
                        *(volatile uint32_t *)address);
        }
    }
    else
    {
        isotp_debug("App有效标志写入Flash失败! 状态: %d\n", status);
    }

    // 5. 锁定Flash
    HAL_FLASH_Lock();
    return result;
}

/**
 * @brief 写入测试数据到指定地址
 * @return HAL状态
 */
HAL_StatusTypeDef FLASH_WriteTestData(void)
{
    //    uint32_t address = APP_VALID_FLAG_ADDRESS;
    //    uint32_t data = APP_VALID_FLAG_ADDRESS;
    //    HAL_StatusTypeDef status;
    //
    //    /* 首先擦除包含该地址的页 */
    //    status = FLASH_ErasePages(address, 1);
    //    if (status != HAL_OK)
    //    {
    //        return status;
    //    }
    //
    //    /* 写入测试数据 */
    //    status = FLASH_WriteWord(address, data);
    //
    //    return status;
}

/**
 * @brief 同时更新 Bootloader 请求标志和 App 有效标志 (使用 Read-Modify-Erase-Write)
 * @retval true 如果更新成功, false 如果失败
 */
bool flash_update_bootloader_flags(void)
{

//    HAL_StatusTypeDef status = HAL_ERROR;
//    uint32_t page_addr;
//    uint32_t page_error = 0;
//    uint32_t app_flag_offset_in_page_dw;  // Offset in Double Words
//    uint32_t boot_flag_offset_in_page_dw; // Offset in Double Words
//    // 使用 Double Word (64-bit) 写入
//    const uint32_t double_words_per_page = FLASH_PAGE_SIZE / sizeof(uint64_t);
//
//    // --- 假设 FLASH_PAGE_SIZE 在某处已定义 ---
//    // --- 假设 BOOTLOADER_REQUEST_MAGIC 和 BOOTLOADER_FLAG_CLEARED 定义 ---
//
//    // 验证地址对齐 (Double Word 需要 8 字节对齐)
//    if (APP_VALID_FLAG_ADDRESS % sizeof(uint64_t) != 0 ||
//        BOOTLOADER_FLAG_ADDRESS % sizeof(uint64_t) != 0)
//    {
//        isotp_debug("错误: 标志地址未按 8 字节对齐!\n");
//        return false;
//    }
//
//    uint32_t app_page = (APP_VALID_FLAG_ADDRESS - FLASH_BASE) / FLASH_PAGE_SIZE;
//    uint32_t boot_page = (BOOTLOADER_FLAG_ADDRESS - FLASH_BASE) / FLASH_PAGE_SIZE;
//    if (app_page != boot_page)
//    {
//        isotp_debug("错误: 两个标志地址不在同一 Flash 页!\n");
//        return false;
//    }
//
//    page_addr = APP_VALID_FLAG_ADDRESS - ((APP_VALID_FLAG_ADDRESS - FLASH_BASE) % FLASH_PAGE_SIZE);
//    // 计算地址在页内的偏移量 (以 Double Word 为单位)
//    app_flag_offset_in_page_dw = (APP_VALID_FLAG_ADDRESS - page_addr) / sizeof(uint64_t);
//    boot_flag_offset_in_page_dw = (BOOTLOADER_FLAG_ADDRESS - page_addr) / sizeof(uint64_t);
//
//    // 使用 uint64_t 缓冲区
//    static uint64_t page_buffer[FLASH_PAGE_SIZE / sizeof(uint64_t)];
//
//    // 1. 解锁Flash
//    if (HAL_FLASH_Unlock() != HAL_OK)
//    {
//        isotp_debug("Flash解锁失败!\n");
//        return false;
//    }
//
//    // 2. 读取整个页到缓冲区 (按 Double Word 读取)
//    for (uint32_t i = 0; i < double_words_per_page; i++)
//    {
//        page_buffer[i] = *(volatile uint64_t *)(page_addr + i * sizeof(uint64_t));
//    }
//
//    // 3. 在缓冲区中修改目标值 (将 32-bit 值放入 64-bit 槽，高位补零)
//    // 注意：这里假设将 32 位标志直接赋给 64 位变量是期望的行为
//    page_buffer[app_flag_offset_in_page_dw] = (uint64_t)BOOTLOADER_FLAG_CLEARED;
//    page_buffer[boot_flag_offset_in_page_dw] = (uint64_t)BOOTLOADER_REQUEST_MAGIC;
//    isotp_debug("缓冲区准备 (DW): Addr 0x%lX -> 0x%llX, Addr 0x%lX -> 0x%llX\n",
//                APP_VALID_FLAG_ADDRESS, (unsigned long long)page_buffer[app_flag_offset_in_page_dw],
//                BOOTLOADER_FLAG_ADDRESS,
//                (unsigned long long)page_buffer[boot_flag_offset_in_page_dw]);
//
//    // 4. 擦除目标页
//    FLASH_EraseInitTypeDef EraseInitStruct;
//    EraseInitStruct.TypeErase = FLASH_TYPEERASE_PAGES;
//    EraseInitStruct.Page = app_page;
//    EraseInitStruct.NbPages = 1;
//    // 对于某些系列 (如 G4)，可能需要指定 Bank
//    EraseInitStruct.Banks = FLASH_BANK_1; // 假设在 Bank 1
//
//    status = HAL_FLASHEx_Erase(&EraseInitStruct, &page_error);
//    if (status != HAL_OK)
//    {
//        isotp_debug("擦除Flash页失败! 页号: %lu, 错误码: 0x%lX\n", EraseInitStruct.Page,
//                    page_error);
//        HAL_FLASH_Lock();
//        return false;
//    }
//    isotp_debug("Flash页 %lu 擦除成功\n", EraseInitStruct.Page);
//
//    // 5. 将整个缓冲区写回Flash页 (Double Word 写入)
//    bool write_ok = true;
//    for (uint32_t i = 0; i < double_words_per_page; i++)
//    {
//        status = HAL_FLASH_Program(FLASH_TYPEPROGRAM_DOUBLEWORD, page_addr + i * sizeof(uint64_t),
//                                   page_buffer[i]);
//        if (status != HAL_OK)
//        {
//            isotp_debug("写入Flash (DW) 失败! 地址: 0x%lX, 状态: %d\n",
//                        page_addr + i * sizeof(uint64_t), status);
//            write_ok = false;
//            break;
//        }
//    }
//
//    // 6. 验证写入结果 (读取原始的 32-bit 地址)
//    bool result = false;
//    if (write_ok)
//    {
//        volatile uint32_t val_app = *(volatile uint32_t *)APP_VALID_FLAG_ADDRESS;
//        volatile uint32_t val_boot = *(volatile uint32_t *)BOOTLOADER_FLAG_ADDRESS;
//
//        if (val_app == BOOTLOADER_FLAG_CLEARED && val_boot == BOOTLOADER_REQUEST_MAGIC)
//        {
//            isotp_debug("Flash双标志写入成功!\n");
//            result = true;
//        }
//        else
//        {
//            isotp_debug(
//                "Flash双标志写入验证失败: App(期望0x%lX, 实际0x%lX), Boot(期望0x%lX, 实际0x%lX)\n",
//                BOOTLOADER_FLAG_CLEARED, val_app, BOOTLOADER_REQUEST_MAGIC, val_boot);
//        }
//    }
//    else
//    {
//        isotp_debug("Flash页写入过程中发生错误。\n");
//    }
//
//
//
//    // 7. 锁定Flash
//    HAL_FLASH_Lock();
//    return result;

}
