../Core/Src/syscalls.c:48:6:initialise_monitor_handles	0	static
../Core/Src/syscalls.c:52:5:_getpid	0	static
../Core/Src/syscalls.c:57:5:_kill	8	static
../Core/Src/syscalls.c:63:6:_exit	8	static
../Core/Src/syscalls.c:69:27:_read	16	static
../Core/Src/syscalls.c:81:27:_write	16	static
../Core/Src/syscalls.c:92:5:_close	0	static
../Core/Src/syscalls.c:98:5:_fstat	0	static
../Core/Src/syscalls.c:104:5:_isatty	0	static
../Core/Src/syscalls.c:109:5:_lseek	0	static
../Core/Src/syscalls.c:114:5:_open	0	static
../Core/Src/syscalls.c:120:5:_wait	8	static
../Core/Src/syscalls.c:126:5:_unlink	8	static
../Core/Src/syscalls.c:132:5:_times	0	static
../Core/Src/syscalls.c:137:5:_stat	0	static
../Core/Src/syscalls.c:143:5:_link	8	static
../Core/Src/syscalls.c:149:5:_fork	8	static
../Core/Src/syscalls.c:155:5:_execve	8	static
