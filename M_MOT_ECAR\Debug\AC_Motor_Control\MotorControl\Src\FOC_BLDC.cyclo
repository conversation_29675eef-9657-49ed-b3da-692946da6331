../AC_Motor_Control/MotorControl/Src/FOC_BLDC.cpp:44:1:CFOC_BLDC::CFOC_BLDC()	3
../AC_Motor_Control/MotorControl/Src/FOC_BLDC.cpp:73:6:void CFOC_BLDC::LoadMtrPrms(const FOCBLDCMtrPrms_t*)	2
../AC_Motor_Control/MotorControl/Src/FOC_BLDC.cpp:98:7:float CFOC_BLDC::GetHallAngl()	1
../AC_Motor_Control/MotorControl/Src/FOC_BLDC.cpp:108:7:float CFOC_BLDC::GetHallTrnsAngl(float, float)	2
../AC_Motor_Control/MotorControl/Src/FOC_BLDC.cpp:124:7:float CFOC_BLDC::GetAnglDiff(float, float)	3
../AC_Motor_Control/MotorControl/Src/FOC_BLDC.cpp:144:6:void CFOC_BLDC::AnglSlope(float*, float, float)	4
../AC_Motor_Control/MotorControl/Src/FOC_BLDC.cpp:180:6:void CFOC_BLDC::AnglSpdCalc()	51
../AC_Motor_Control/MotorControl/Src/FOC_BLDC.cpp:493:6:void CFOC_BLDC::NorPrcs()	6
../AC_Motor_Control/MotorControl/Src/FOC_BLDC.cpp:570:6:void CFOC_BLDC::ShtDwnPrcs()	2
../AC_Motor_Control/MotorControl/Src/FOC_BLDC.cpp:621:6:void CFOC_BLDC::ClbPrcs()	2
../AC_Motor_Control/MotorControl/Src/FOC_BLDC.cpp:673:6:void CFOC_BLDC::IdIqDrctCtl(float, float)	2
../AC_Motor_Control/MotorControl/Src/FOC_BLDC.cpp:710:6:void setFOCBLDCPrms_t(const FOCBLDCPrms_t*)	2
