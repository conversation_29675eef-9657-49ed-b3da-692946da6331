../Modules/Src/uds.c:877:13:CheckPreconditions	0	static
../Modules/Src/uds.c:78:6:isotp_debug	0	static
../Modules/Src/uds.c:117:18:UDS_BuildResponse	96	static
../Modules/Src/uds.c:176:18:UDS_BuildNegativeResponse	88	static
../Modules/Src/uds.c:440:25:HandleReadDataByIdentifier	96	static
../Modules/Src/uds.c:345:25:HandleDTCControl	16	static
../Modules/Src/uds.c:392:25:HandleRoutineControl	40	static
../Modules/Src/uds.c:263:25:HandleCommunicationControl	16	static
../Modules/Src/uds.c:197:25:HandleSessionControl	56	static
../Modules/Src/uds.c:850:6:UDS_Init	0	static
../Modules/Src/uds.c:859:20:UDS_GetCurrentSession	0	static
../Modules/Src/uds.c:868:6:UDS_SetCurrentSession	0	static
../Modules/Src/uds.c:1047:9:UDS_SendPollingFrame	0	static
../Modules/Src/uds.c:989:13:ISO_TP_SendConsecutiveFrame	32	static
../Modules/Src/uds.c:951:13:ISO_TP_HandleFlowControl	8	static
../Modules/Src/uds.c:890:13:ISO_TP_SendFirstFrame	24	static
../Modules/Src/uds.c:599:12:ProcessUDSRequest	208	static
../Modules/Src/uds.c:516:12:UDS_ProcessMessage	8	static
