################################################################################
# Automatically-generated file. Do not edit!
# Toolchain: GNU Tools for STM32 (13.3.rel1)
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
CPP_SRCS += \
../LogicControl/Src/DriverOutput.cpp 

C_SRCS += \
../LogicControl/Src/Can.c \
../LogicControl/Src/DataProcess.c \
../LogicControl/Src/FaultLight.c \
../LogicControl/Src/GetInput.c \
../LogicControl/Src/HPM.c \
../LogicControl/Src/Ladrc.c \
../LogicControl/Src/MsgProcess.c \
../LogicControl/Src/OtherFunction.c \
../LogicControl/Src/PI.c \
../LogicControl/Src/Store.c \
../LogicControl/Src/Vardefine.c \
../LogicControl/Src/faultprocess.c \
../LogicControl/Src/speedmode.c \
../LogicControl/Src/torquemode.c 

C_DEPS += \
./LogicControl/Src/Can.d \
./LogicControl/Src/DataProcess.d \
./LogicControl/Src/FaultLight.d \
./LogicControl/Src/GetInput.d \
./LogicControl/Src/HPM.d \
./LogicControl/Src/Ladrc.d \
./LogicControl/Src/MsgProcess.d \
./LogicControl/Src/OtherFunction.d \
./LogicControl/Src/PI.d \
./LogicControl/Src/Store.d \
./LogicControl/Src/Vardefine.d \
./LogicControl/Src/faultprocess.d \
./LogicControl/Src/speedmode.d \
./LogicControl/Src/torquemode.d 

OBJS += \
./LogicControl/Src/Can.o \
./LogicControl/Src/DataProcess.o \
./LogicControl/Src/DriverOutput.o \
./LogicControl/Src/FaultLight.o \
./LogicControl/Src/GetInput.o \
./LogicControl/Src/HPM.o \
./LogicControl/Src/Ladrc.o \
./LogicControl/Src/MsgProcess.o \
./LogicControl/Src/OtherFunction.o \
./LogicControl/Src/PI.o \
./LogicControl/Src/Store.o \
./LogicControl/Src/Vardefine.o \
./LogicControl/Src/faultprocess.o \
./LogicControl/Src/speedmode.o \
./LogicControl/Src/torquemode.o 

CPP_DEPS += \
./LogicControl/Src/DriverOutput.d 


# Each subdirectory must supply rules for building sources it contributes
LogicControl/Src/%.o LogicControl/Src/%.su LogicControl/Src/%.cyclo: ../LogicControl/Src/%.c LogicControl/Src/subdir.mk
	arm-none-eabi-gcc "$<" -mcpu=cortex-m4 -std=gnu11 -g3 -DSTM32G431xx -DUSE_HAL_DRIVER -DDEBUG -c -I../Core/Inc -I../Drivers/STM32G4xx_HAL_Driver/Inc -I../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I../Drivers/CMSIS/Include -I../AC_Motor_Control -I../STM32G4_Drivers -I../LogicControl/Inc -I../Devices -I../Modules/Inc -Og -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
LogicControl/Src/%.o LogicControl/Src/%.su LogicControl/Src/%.cyclo: ../LogicControl/Src/%.cpp LogicControl/Src/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32G431xx -DUSE_HAL_DRIVER -DDEBUG -c -I../Core/Inc -I../Drivers/STM32G4xx_HAL_Driver/Inc -I../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I../Drivers/CMSIS/Include -I../AC_Motor_Control -I../STM32G4_Drivers -I../LogicControl/Inc -I../Devices -I../Modules/Inc -Og -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-threadsafe-statics -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"

clean: clean-LogicControl-2f-Src

clean-LogicControl-2f-Src:
	-$(RM) ./LogicControl/Src/Can.cyclo ./LogicControl/Src/Can.d ./LogicControl/Src/Can.o ./LogicControl/Src/Can.su ./LogicControl/Src/DataProcess.cyclo ./LogicControl/Src/DataProcess.d ./LogicControl/Src/DataProcess.o ./LogicControl/Src/DataProcess.su ./LogicControl/Src/DriverOutput.cyclo ./LogicControl/Src/DriverOutput.d ./LogicControl/Src/DriverOutput.o ./LogicControl/Src/DriverOutput.su ./LogicControl/Src/FaultLight.cyclo ./LogicControl/Src/FaultLight.d ./LogicControl/Src/FaultLight.o ./LogicControl/Src/FaultLight.su ./LogicControl/Src/GetInput.cyclo ./LogicControl/Src/GetInput.d ./LogicControl/Src/GetInput.o ./LogicControl/Src/GetInput.su ./LogicControl/Src/HPM.cyclo ./LogicControl/Src/HPM.d ./LogicControl/Src/HPM.o ./LogicControl/Src/HPM.su ./LogicControl/Src/Ladrc.cyclo ./LogicControl/Src/Ladrc.d ./LogicControl/Src/Ladrc.o ./LogicControl/Src/Ladrc.su ./LogicControl/Src/MsgProcess.cyclo ./LogicControl/Src/MsgProcess.d ./LogicControl/Src/MsgProcess.o ./LogicControl/Src/MsgProcess.su ./LogicControl/Src/OtherFunction.cyclo ./LogicControl/Src/OtherFunction.d ./LogicControl/Src/OtherFunction.o ./LogicControl/Src/OtherFunction.su ./LogicControl/Src/PI.cyclo ./LogicControl/Src/PI.d ./LogicControl/Src/PI.o ./LogicControl/Src/PI.su ./LogicControl/Src/Store.cyclo ./LogicControl/Src/Store.d ./LogicControl/Src/Store.o ./LogicControl/Src/Store.su ./LogicControl/Src/Vardefine.cyclo ./LogicControl/Src/Vardefine.d ./LogicControl/Src/Vardefine.o ./LogicControl/Src/Vardefine.su ./LogicControl/Src/faultprocess.cyclo ./LogicControl/Src/faultprocess.d ./LogicControl/Src/faultprocess.o ./LogicControl/Src/faultprocess.su ./LogicControl/Src/speedmode.cyclo ./LogicControl/Src/speedmode.d ./LogicControl/Src/speedmode.o ./LogicControl/Src/speedmode.su ./LogicControl/Src/torquemode.cyclo ./LogicControl/Src/torquemode.d ./LogicControl/Src/torquemode.o ./LogicControl/Src/torquemode.su

.PHONY: clean-LogicControl-2f-Src

