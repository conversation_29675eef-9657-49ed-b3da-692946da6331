../Core/Src/syscalls.c:48:6:initialise_monitor_handles	1
../Core/Src/syscalls.c:52:5:_getpid	1
../Core/Src/syscalls.c:57:5:_kill	1
../Core/Src/syscalls.c:63:6:_exit	1
../Core/Src/syscalls.c:69:27:_read	2
../Core/Src/syscalls.c:81:27:_write	2
../Core/Src/syscalls.c:92:5:_close	1
../Core/Src/syscalls.c:98:5:_fstat	1
../Core/Src/syscalls.c:104:5:_isatty	1
../Core/Src/syscalls.c:109:5:_lseek	1
../Core/Src/syscalls.c:114:5:_open	1
../Core/Src/syscalls.c:120:5:_wait	1
../Core/Src/syscalls.c:126:5:_unlink	1
../Core/Src/syscalls.c:132:5:_times	1
../Core/Src/syscalls.c:137:5:_stat	1
../Core/Src/syscalls.c:143:5:_link	1
../Core/Src/syscalls.c:149:5:_fork	1
../Core/Src/syscalls.c:155:5:_execve	1
