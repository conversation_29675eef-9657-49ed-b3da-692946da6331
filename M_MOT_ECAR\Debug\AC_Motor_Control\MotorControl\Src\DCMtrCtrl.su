../AC_Motor_Control/MotorControl/Src/DCMtrCtrl.cpp:37:1:CDCMtrCtrl::CDCMtrCtrl()	16	static
../AC_Motor_Control/MotorControl/Src/DCMtrCtrl.cpp:507:1:void __static_initialization_and_destruction_0()	16	static
../AC_Motor_Control/MotorControl/Src/DCMtrCtrl.cpp:54:6:void CDCMtrCtrl::Init()	16	static
../AC_Motor_Control/MotorControl/Src/DCMtrCtrl.cpp:133:6:void CDCMtrCtrl::Prcs_PWMInt()	16	static
../AC_Motor_Control/MotorControl/Src/DCMtrCtrl.cpp:429:6:static void CDCMtrCtrl::SCOpnTstRdyChk()	0	static
../AC_Motor_Control/MotorControl/Src/DCMtrCtrl.cpp:456:6:void DCMtrCtrlInit()	8	static
../AC_Motor_Control/MotorControl/Src/DCMtrCtrl.cpp:472:6:void DCMtrCtrlCrntISR(uint8_t)	8	static
../AC_Motor_Control/MotorControl/Src/DCMtrCtrl.cpp:481:6:void DCMtrCtrlAllISR()	8	static
../AC_Motor_Control/MotorControl/Src/DCMtrCtrl.cpp:494:6:void DCMtrCtrlMnLpFst()	8	static
../AC_Motor_Control/MotorControl/Src/DCMtrCtrl.cpp:499:6:void setDCMtrCtrlPrms_t(const DCMtrCtrlPrms_t*, size_t)	16	static
../STM32G4_Drivers/Inc\Timer.h:58:6:void CTimer<CntType>::SetStart() [with CntType = short unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:89:9:uint8_t CTimer<CntType>::CheckTimeElps(CntType) [with CntType = short unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:71:9:CntType CTimer<CntType>::GetTimeElapsed(uint8_t) [with CntType = short unsigned int]	0	static
../AC_Motor_Control/MotorControl/Src/DCMtrCtrl.cpp:316:6:void CDCMtrCtrl::SCOpenTst()	56	static
../AC_Motor_Control/MotorControl/Src/DCMtrCtrl.cpp:413:6:static void CDCMtrCtrl::SCOpnTstPrcs()	8	static
../AC_Motor_Control/MotorControl/Src/DCMtrCtrl.cpp:507:1:cpp)	8	static
