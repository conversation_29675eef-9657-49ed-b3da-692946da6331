../Devices/Src/AD2S1205.cpp:21:1:CAD2S1205::CAD2S1205()	24	static
../Devices/Src/AD2S1205.cpp:107:6:void CAD2S1205::SelectCh(DualDrvCh_t)	0	static
../Devices/Src/AD2S1205.cpp:176:11:PrcsStt_t CAD2S1205::FltReset()	0	static
../Devices/Src/AD2S1205.cpp:203:9:uint8_t CAD2S1205::CheckFlt()	0	static
../Devices/Src/AD2S1205.cpp:217:6:void CAD2S1205::DisableFlt()	0	static
../Devices/Src/AD2S1205.cpp:225:6:void CAD2S1205::EnableFlt()	0	static
../STM32G4_Drivers/Inc\Timer.h:58:6:void CTimer<CntType>::SetStart() [with CntType = short unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:89:9:uint8_t CTimer<CntType>::CheckTimeElps(CntType) [with CntType = short unsigned int]	0	static
../Devices/Src/AD2S1205.cpp:49:11:PrcsStt_t CAD2S1205::Init()	32	static
../Devices/Src/AD2S1205.cpp:121:11:PrcsStt_t CAD2S1205::GetAngle(uint16_t*)	16	static
