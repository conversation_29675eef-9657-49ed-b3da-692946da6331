../AC_Motor_Control/MotorControl/Src/FOC_IM_V1.cpp:26:1:CFOC_IM_V1::CFOC_IM_V1()	1
../AC_Motor_Control/MotorControl/Src/FOC_IM_V1.cpp:49:6:void CFOC_IM_V1::MtrPrmsInit()	2
../AC_Motor_Control/MotorControl/Src/FOC_IM_V1.cpp:38:6:void CFOC_IM_V1::LoadMtrPrms(const FOCIMV1MtrPrms_t*)	1
../AC_Motor_Control/MotorControl/Src/FOC_IM_V1.cpp:86:6:void CFOC_IM_V1::IqRefCalc()	6
../AC_Motor_Control/MotorControl/Src/FOC_IM_V1.cpp:323:6:void setFOCIMV1Prms_t(const FOCIMV1Prms_t*)	2
../STM32G4_Drivers/Inc\Timer.h:58:6:void CTimer<CntType>::SetStart() [with CntType = short unsigned int]	1
../STM32G4_Drivers/Inc\Timer.h:71:9:CntType CTimer<CntType>::GetTimeElapsed(uint8_t) [with CntType = short unsigned int]	2
../STM32G4_Drivers/Inc\Timer.h:106:6:void CTimer<CntType>::SaveSmpToRec() [with CntType = short unsigned int]	1
../AC_Motor_Control/MotorControl/Src/..\Inc\../../Common/Inc/UserMaths.h:145:7:float CDifferential<InType>::GetVal() [with InType = float]	8
../AC_Motor_Control/MotorControl/Src/FOC_IM_V1.cpp:119:6:void CFOC_IM_V1::NorPrcs()	5
../AC_Motor_Control/MotorControl/Src/FOC_IM_V1.cpp:193:6:void CFOC_IM_V1::ShtDwnPrcs()	2
../AC_Motor_Control/MotorControl/Src/FOC_IM_V1.cpp:237:6:void CFOC_IM_V1::IdIqDrctCtl(float, float)	9
