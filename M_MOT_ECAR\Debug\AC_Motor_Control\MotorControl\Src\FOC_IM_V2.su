../AC_Motor_Control/MotorControl/Src/FOC_IM_V2.cpp:24:1:CFOC_IM_V2::CFOC_IM_V2()	8	static
../AC_Motor_Control/MotorControl/Src/FOC_IM_V2.cpp:233:6:void setFOCIMV2Prms_t(const FOCIMV2Prms_t*)	4	static
../STM32G4_Drivers/Inc\Timer.h:58:6:void CTimer<CntType>::SetStart() [with CntType = short unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:71:9:CntType CTimer<CntType>::GetTimeElapsed(uint8_t) [with CntType = short unsigned int]	0	static
../STM32G4_Drivers/Inc\Timer.h:106:6:void CTimer<CntType>::SaveSmpToRec() [with CntType = short unsigned int]	0	static
../AC_Motor_Control/MotorControl/Src/../Inc/../../Common/Inc/UserMaths.h:145:7:float CDifferential<InType>::GetVal() [with InType = float]	24	static
../AC_Motor_Control/MotorControl/Src/FOC_IM_V2.cpp:38:6:void CFOC_IM_V2::NorPrcs()	16	static
../AC_Motor_Control/MotorControl/Src/FOC_IM_V2.cpp:126:6:void CFOC_IM_V2::IdIqDrctCtl(float, float)	24	static
../AC_Motor_Control/MotorControl/Src/FOC_IM_V2.cpp:182:6:void CFOC_IM_V2::ShtDwnPrcs()	8	static
