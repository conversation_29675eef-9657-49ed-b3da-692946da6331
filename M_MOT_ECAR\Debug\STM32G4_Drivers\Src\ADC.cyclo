../STM32G4_Drivers/Src/ADC.cpp:28:6:static void CADC::Init()	4
../STM32G4_Drivers/Src/ADC.cpp:73:9:uint8_t CADC::GetSeqVal(ADC_TypeDef*, uint8_t)	1
../STM32G4_Drivers/Src/ADC.cpp:87:1:CADC_Prprtn::CADC_Prprtn()	1
../STM32G4_Drivers/Src/ADC.cpp:94:1:CADC_Prprtn::CADC_Prprtn(uint8_t, float)	1
../STM32G4_Drivers/Src/ADC.cpp:101:7:float CADC_Prprtn::GetVal()	1
../STM32G4_Drivers/Src/ADC.cpp:113:11:PrcsStt_t CADC_Prprtn::OffstSmp()	2
../STM32G4_Drivers/Src/ADC.cpp:137:6:void CADC_Prprtn::SetSWTrig()	4
../STM32G4_Drivers/Src/ADC.cpp:187:6:void CADC_Prprtn::SetPWMTrig()	3
../STM32G4_Drivers/Src/ADC.cpp:231:6:void CADC_Prprtn::SWTrig()	1
../STM32G4_Drivers/Src/ADC.cpp:242:6:void CADC_Prprtn::WaitCnvrt()	2
../STM32G4_Drivers/Src/ADC.cpp:251:7:float CADC_Prprtn::GetVal_SWTrg()	1
../STM32G4_Drivers/Src/ADC.cpp:260:6:void CADC_Prprtn::ClrIntFlg()	1
../STM32G4_Drivers/Src/ADC.cpp:271:1:CADCCh::CADCCh()	1
../STM32G4_Drivers/Src/ADC.cpp:276:1:CADCCh::CADCCh(ADCRglrSqCfg_t)	1
../STM32G4_Drivers/Src/ADC.cpp:281:6:void CADCCh::SetChannel(uint8_t)	1
../STM32G4_Drivers/Src/ADC.cpp:289:10:uint16_t CADCCh::GetResult()	1
../STM32G4_Drivers/Src/ADC.cpp:297:7:float CADCCh::GetVolt()	1
../STM32G4_Drivers/Src/ADC.cpp:308:1:CPhsCrnt::CPhsCrnt()	1
../STM32G4_Drivers/Src/ADC.cpp:333:6:void CPhsCrnt::SelectCh(DualDrvCh_t)	2
../STM32G4_Drivers/Src/ADC.cpp:352:6:void CPhsCrnt::SetISRCall(void (*)())	1
../STM32G4_Drivers/Src/ADC.cpp:361:11:PrcsStt_t CPhsCrnt::OffsetSmp()	6
../STM32G4_Drivers/Src/ADC.cpp:407:6:void CPhsCrnt::SetPhsSeq(PhsCrntSnsrSq_t)	7
../STM32G4_Drivers/Src/ADC.cpp:453:6:void CPhsCrnt::GetVal()	3
../STM32G4_Drivers/Src/ADC.cpp:484:6:static void CPhsCrnt::SetSWTrig()	3
../STM32G4_Drivers/Src/ADC.cpp:517:6:static void CPhsCrnt::SetPWMTrig()	3
../STM32G4_Drivers/Src/ADC.cpp:559:6:void CPhsCrnt::IRQEn()	1
../STM32G4_Drivers/Src/ADC.cpp:577:6:void CPhsCrnt::IRQDis()	1
../STM32G4_Drivers/Src/ADC.cpp:595:6:void CPhsCrnt::SWTrig()	1
../STM32G4_Drivers/Src/ADC.cpp:628:6:void CPhsCrnt::WaitCnvrt()	2
../STM32G4_Drivers/Src/ADC.cpp:646:6:void CPhsCrnt::ClrIntFlg()	1
