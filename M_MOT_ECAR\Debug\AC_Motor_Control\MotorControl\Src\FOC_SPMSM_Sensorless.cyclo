../AC_Motor_Control/MotorControl/Src/FOC_SPMSM_Sensorless.cpp:19:1:CFOC_SPMSM_Snsls::CFOC_SPMSM_Snsls()	1
../AC_Motor_Control/MotorControl/Src/FOC_SPMSM_Sensorless.cpp:28:6:void CFOC_SPMSM_Snsls::LoadMtrPrms(const FOCSPMSMSnslsMtrPrms_t*)	1
../AC_Motor_Control/MotorControl/Src/FOC_SPMSM_Sensorless.cpp:46:6:void CFOC_SPMSM_Snsls::FluxObserver()	2
../AC_Motor_Control/MotorControl/Src/FOC_SPMSM_Sensorless.cpp:246:6:void setFOCSPMSMSnslsPrms_t(const FOCSPMSMSnslsPrms_t*)	2
../STM32G4_Drivers/Inc\Timer.h:58:6:void CTimer<CntType>::SetStart() [with CntType = short unsigned int]	1
../STM32G4_Drivers/Inc\Timer.h:71:9:CntType CTimer<CntType>::GetTimeElapsed(uint8_t) [with CntType = short unsigned int]	2
../STM32G4_Drivers/Inc\Timer.h:106:6:void CTimer<CntType>::SaveSmpToRec() [with CntType = short unsigned int]	1
../AC_Motor_Control/MotorControl/Src/../Inc/../../Common/Inc/UserMaths.h:145:7:float CDifferential<InType>::GetVal() [with InType = float]	8
../AC_Motor_Control/MotorControl/Src/FOC_SPMSM_Sensorless.cpp:68:6:void CFOC_SPMSM_Snsls::NorPrcs()	8
../AC_Motor_Control/MotorControl/Src/FOC_SPMSM_Sensorless.cpp:187:6:void CFOC_SPMSM_Snsls::ShtDwnPrcs()	2
../AC_Motor_Control/MotorControl/Src/FOC_SPMSM_Sensorless.cpp:233:6:void CFOC_SPMSM_Snsls::ClbPrcs()	1
../AC_Motor_Control/MotorControl/Src/FOC_SPMSM_Sensorless.cpp:241:6:void CFOC_SPMSM_Snsls::IdIqDrctCtl(float, float)	1
