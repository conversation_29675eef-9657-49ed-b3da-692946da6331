################################################################################
# Automatically-generated file. Do not edit!
# Toolchain: GNU Tools for STM32 (13.3.rel1)
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../Modules/Src/flash_handler.c \
../Modules/Src/uds.c 

C_DEPS += \
./Modules/Src/flash_handler.d \
./Modules/Src/uds.d 

OBJS += \
./Modules/Src/flash_handler.o \
./Modules/Src/uds.o 


# Each subdirectory must supply rules for building sources it contributes
Modules/Src/%.o Modules/Src/%.su Modules/Src/%.cyclo: ../Modules/Src/%.c Modules/Src/subdir.mk
	arm-none-eabi-gcc "$<" -mcpu=cortex-m4 -std=gnu11 -g3 -DSTM32G431xx -DUSE_HAL_DRIVER -DDEBUG -c -I../Core/Inc -I../Drivers/STM32G4xx_HAL_Driver/Inc -I../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I../Drivers/CMSIS/Include -I../AC_Motor_Control -I../STM32G4_Drivers -I../LogicControl/Inc -I../Devices -I../Modules/Inc -Og -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"

clean: clean-Modules-2f-Src

clean-Modules-2f-Src:
	-$(RM) ./Modules/Src/flash_handler.cyclo ./Modules/Src/flash_handler.d ./Modules/Src/flash_handler.o ./Modules/Src/flash_handler.su ./Modules/Src/uds.cyclo ./Modules/Src/uds.d ./Modules/Src/uds.o ./Modules/Src/uds.su

.PHONY: clean-Modules-2f-Src

